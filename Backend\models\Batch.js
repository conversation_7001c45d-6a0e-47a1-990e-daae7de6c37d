const mongoose = require('mongoose');

const batchSchema = new mongoose.Schema({
    batchName: {
        type: String,
        required: true,
        trim: true
    },
    batchId: {
        type: String,
        required: true,
        unique: true, // Unique batch code
        trim: true
    },
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true, // Make courseId required for proper relationship
        validate: {
            validator: function(v) {
                return mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid course ID format'
        }
    },
    trainerId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Must be a user with role: "trainer"
        validate: {
            validator: function(v) {
                return !v || mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid trainer ID format'
        }
    },
    students: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Must be users with role: "student"
        validate: {
            validator: function(v) {
                return mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid student ID format'
        }
    }],
    maxStudents: {
        type: Number,
        required: true,
        min: [1, 'Maximum students must be at least 1'],
        max: [500, 'Maximum students cannot exceed 500']
    },
    currentStudentCount: {
        type: Number,
        default: 0,
        min: 0
    },
    startDate: {
        type: Date,
        required: true,
        validate: {
            validator: function(v) {
                return v instanceof Date && !isNaN(v);
            },
            message: 'Invalid start date'
        }
    },
    endDate: {
        type: Date,
        required: true,
        validate: {
            validator: function(v) {
                return v instanceof Date && !isNaN(v) && v > this.startDate;
            },
            message: 'End date must be after start date'
        }
    },
    location: {
        type: String,
        trim: true
    },
    schedule: {
        type: String,
        trim: true // e.g., "Mon-Fri 9AM-5PM"
    },
    description: {
        type: String,
        trim: true,
        maxlength: [1000, 'Description cannot exceed 1000 characters']
    },
    department: {
        type: String,
        trim: true
    },
    collegeId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'College',
        validate: {
            validator: function(v) {
                return !v || mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid college ID format'
        }
    },
    status: {
        type: String,
        enum: ['upcoming', 'ongoing', 'completed', 'cancelled'],
        default: 'upcoming'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        validate: {
            validator: function(v) {
                return mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid creator ID format'
        }
    }
}, {
    timestamps: true
});

// Pre-save middleware to update currentStudentCount
batchSchema.pre('save', function(next) {
    if (this.students) {
        this.currentStudentCount = this.students.length;
    }
    next();
});

// Index for better query performance
batchSchema.index({ batchId: 1 });
batchSchema.index({ courseId: 1 });
batchSchema.index({ trainerId: 1 });
batchSchema.index({ status: 1 });
batchSchema.index({ startDate: 1 });
batchSchema.index({ isActive: 1 });

// Compound indexes for common queries
batchSchema.index({ courseId: 1, status: 1 });
batchSchema.index({ trainerId: 1, status: 1 });

module.exports = mongoose.model('Batch', batchSchema);
