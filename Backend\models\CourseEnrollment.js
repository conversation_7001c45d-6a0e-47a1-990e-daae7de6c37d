const mongoose = require('mongoose');

const courseEnrollmentSchema = new mongoose.Schema({
    courseId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Course',
        required: true,
        validate: {
            validator: function(v) {
                return mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid course ID format'
        }
    },
    studentId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        validate: {
            validator: function(v) {
                return mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid student ID format'
        }
    },
    enrollmentDate: {
        type: Date,
        default: Date.now,
        required: true
    },
    status: {
        type: String,
        enum: ['enrolled', 'assigned_to_batch', 'completed', 'dropped', 'transferred'],
        default: 'enrolled'
    },
    batchId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Batch',
        default: null,
        validate: {
            validator: function(v) {
                return !v || mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid batch ID format'
        }
    },
    enrolledBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        validate: {
            validator: function(v) {
                return mongoose.Types.ObjectId.isValid(v);
            },
            message: 'Invalid enrolledBy ID format'
        }
    },
    assignedToBatchDate: {
        type: Date,
        default: null
    },
    completionDate: {
        type: Date,
        default: null
    },
    dropDate: {
        type: Date,
        default: null
    },
    dropReason: {
        type: String,
        trim: true
    },
    notes: {
        type: String,
        trim: true,
        maxlength: [500, 'Notes cannot exceed 500 characters']
    },
    isActive: {
        type: Boolean,
        default: true
    }
}, {
    timestamps: true
});

// Compound index to prevent duplicate enrollments
courseEnrollmentSchema.index({ courseId: 1, studentId: 1 }, { unique: true });

// Indexes for better query performance
courseEnrollmentSchema.index({ courseId: 1, status: 1 });
courseEnrollmentSchema.index({ studentId: 1, status: 1 });
courseEnrollmentSchema.index({ batchId: 1 });
courseEnrollmentSchema.index({ enrollmentDate: 1 });
courseEnrollmentSchema.index({ isActive: 1 });

// Pre-save middleware to update status-related dates
courseEnrollmentSchema.pre('save', function(next) {
    if (this.isModified('status')) {
        const now = new Date();
        
        switch (this.status) {
            case 'assigned_to_batch':
                if (!this.assignedToBatchDate) {
                    this.assignedToBatchDate = now;
                }
                break;
            case 'completed':
                if (!this.completionDate) {
                    this.completionDate = now;
                }
                break;
            case 'dropped':
                if (!this.dropDate) {
                    this.dropDate = now;
                }
                break;
        }
    }
    next();
});

// Static method to get enrolled students for a course
courseEnrollmentSchema.statics.getEnrolledStudents = function(courseId, status = 'enrolled') {
    return this.find({ courseId, status, isActive: true })
        .populate('studentId', 'name email mobile department preferredLocation')
        .sort({ enrollmentDate: 1 });
};

// Static method to get unassigned students for a course
courseEnrollmentSchema.statics.getUnassignedStudents = function(courseId) {
    return this.find({ 
        courseId, 
        status: 'enrolled', 
        batchId: null, 
        isActive: true 
    })
    .populate('studentId', 'name email mobile department preferredLocation')
    .sort({ enrollmentDate: 1 });
};

// Static method to get course enrollment statistics
courseEnrollmentSchema.statics.getCourseStats = function (courseId) {
    return this.aggregate([
        { $match: { courseId: new mongoose.Types.ObjectId(courseId), isActive: true } },
        {
            $group: {
                _id: '$status',
                count: { $sum: 1 }
            }
        },
        {
            $group: {
                _id: null,
                totalEnrolled: { $sum: '$count' },
                statusBreakdown: {
                    $push: {
                        status: '$_id',
                        count: '$count'
                    }
                }
            }
        }
    ]);
};


// Instance method to assign student to batch
courseEnrollmentSchema.methods.assignToBatch = function(batchId) {
    this.batchId = batchId;
    this.status = 'assigned_to_batch';
    this.assignedToBatchDate = new Date();
    return this.save();
};

// Instance method to transfer student to different batch
courseEnrollmentSchema.methods.transferToBatch = function(newBatchId, reason) {
    this.batchId = newBatchId;
    this.status = 'transferred';
    this.notes = reason || 'Transferred to different batch';
    return this.save();
};

module.exports = mongoose.model('CourseEnrollment', courseEnrollmentSchema);
