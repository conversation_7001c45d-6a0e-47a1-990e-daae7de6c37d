const asyncHandler = require('../utils/asyncHandler');
const ApiError = require('../utils/ApiError.js');
const ApiResponse = require('../utils/ApiResponse.js');
const User = require('../models/User');
const Batch = require('../models/Batch');
const Course = require('../models/Course');
const CourseEnrollment = require('../models/CourseEnrollment');
const Assignment = require('../models/Assignment');
const AssignmentSubmission = require('../models/AssignmentSubmission');
const Attendance = require('../models/Attendance');
const Assessment = require('../models/Assessment');
const CourseMaterial = require('../models/CourseMaterial');
const Notification = require('../models/Notification');
const Feedback = require('../models/Feedback');
const { uploadToCloudinary } = require('../middlewares/fileUpload.js'); // Changed from uploadToFirebase
const mongoose = require('mongoose');
const { cloudinaryBufferUpload } = require('../middlewares/uploadMiddleware.js');

// ================================
// DASHBOARD
// ================================
const getDashboard = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;

    // Get assigned batches with student count
    const assignedBatches = await Batch.find({ trainer: trainerId })
        .populate('course', 'title description')
        .populate('students', 'firstName lastName')
        .select('batchId courseName startDate endDate status students');

    // Calculate statistics
    const totalBatches = assignedBatches.length;
    const activeBatches = assignedBatches.filter(batch => batch.status === 'ongoing').length;
    const totalStudents = assignedBatches.reduce((sum, batch) => sum + batch.students.length, 0);

    // Get recent assignments
    const recentAssignments = await Assignment.find({ uploadedBy: trainerId })
        .populate('batch', 'batchId courseName')
        .sort({ createdAt: -1 })
        .limit(5);

    // Get pending submissions count
    const pendingSubmissions = await AssignmentSubmission.countDocuments({
        assignment: { $in: recentAssignments.map(a => a._id) },
        isGraded: false
    });

    // Get recent notifications
    const recentNotifications = await Notification.find({ createdBy: trainerId })
        .sort({ createdAt: -1 })
        .limit(5);

    const dashboardData = {
        stats: {
            totalBatches,
            activeBatches,
            totalStudents,
            pendingSubmissions
        },
        assignedBatches: assignedBatches.map(batch => ({
            _id: batch._id,
            batchId: batch.batchId,
            courseName: batch.courseName,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status,
            studentCount: batch.students.length,
            course: batch.course
        })),
        recentAssignments,
        recentNotifications
    };

    res.status(200).json(new ApiResponse(200, dashboardData, 'Dashboard data retrieved successfully'));
});

// ================================
// ENHANCED BATCH MANAGEMENT
// ================================

// Get only trainer's assigned batches with enhanced filtering
const getMyAssignedBatches = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;
    const {
        status,
        courseId,
        page = 1,
        limit = 10,
        search,
        sortBy = 'startDate',
        sortOrder = 'asc'
    } = req.query;

    // Build filter for trainer's batches only
    const filter = {
        trainerId: trainerId,
        isActive: true
    };

    if (status) filter.status = status;
    if (courseId && mongoose.Types.ObjectId.isValid(courseId)) {
        filter.courseId = courseId;
    }

    // Add search functionality
    if (search) {
        filter.$or = [
            { batchName: { $regex: search, $options: 'i' } },
            { batchId: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
        ];
    }

    // Build sort object
    const sortObj = {};
    sortObj[sortBy] = sortOrder === 'desc' ? -1 : 1;

    const batches = await Batch.find(filter)
        .populate({
            path: 'courseId',
            select: 'title courseCode duration department difficulty'
        })
        .populate({
            path: 'students',
            select: 'name.first name.last email userId department',
            match: { isActive: true }
        })
        .sort(sortObj)
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean();

    const totalBatches = await Batch.countDocuments(filter);

    // Add computed fields
    const enrichedBatches = batches.map(batch => ({
        ...batch,
        currentStudentCount: batch.students ? batch.students.length : 0,
        isUpcoming: batch.status === 'upcoming',
        isOngoing: batch.status === 'ongoing',
        isCompleted: batch.status === 'completed',
        daysUntilStart: batch.startDate ? Math.ceil((new Date(batch.startDate) - new Date()) / (1000 * 60 * 60 * 24)) : null,
        duration: batch.endDate && batch.startDate ?
            Math.ceil((new Date(batch.endDate) - new Date(batch.startDate)) / (1000 * 60 * 60 * 24)) : null
    }));

    res.status(200).json(
        new ApiResponse(200, {
            batches: enrichedBatches,
            pagination: {
                totalPages: Math.ceil(totalBatches / limit),
                currentPage: parseInt(page),
                totalBatches,
                hasNextPage: page < Math.ceil(totalBatches / limit),
                hasPrevPage: page > 1
            }
        }, 'Assigned batches retrieved successfully')
    );
});

// Get detailed information about a specific assigned batch
const getMyBatchDetails = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Validate batchId
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    // Find batch and verify trainer ownership
    const batch = await Batch.findOne({
        _id: batchId,
        trainerId: trainerId,
        isActive: true
    })
    .populate({
        path: 'courseId',
        select: 'title courseCode duration department difficulty description objectives'
    })
    .populate({
        path: 'students',
        select: 'name.first name.last email userId mobile department preferredLocation status',
        match: { isActive: true }
    })
    .lean();

    if (!batch) {
        throw new ApiError(404, 'Batch not found or you are not assigned to this batch');
    }

    // Get course enrollment details for students
    const studentEnrollments = await CourseEnrollment.find({
        courseId: batch.courseId._id,
        studentId: { $in: batch.students.map(s => s._id) },
        isActive: true
    }).lean();

    // Merge enrollment data with student data
    const studentsWithEnrollment = batch.students.map(student => {
        const enrollment = studentEnrollments.find(e => e.studentId.toString() === student._id.toString());
        return {
            ...student,
            enrollmentDate: enrollment?.enrollmentDate,
            enrollmentStatus: enrollment?.status,
            assignedToBatchDate: enrollment?.assignedToBatchDate
        };
    });

    // Calculate batch statistics
    const stats = {
        totalStudents: batch.students.length,
        maxStudents: batch.maxStudents,
        occupancyRate: batch.maxStudents > 0 ? Math.round((batch.students.length / batch.maxStudents) * 100) : 0,
        daysUntilStart: batch.startDate ? Math.ceil((new Date(batch.startDate) - new Date()) / (1000 * 60 * 60 * 24)) : null,
        daysRemaining: batch.endDate ? Math.ceil((new Date(batch.endDate) - new Date()) / (1000 * 60 * 60 * 24)) : null,
        totalDuration: batch.endDate && batch.startDate ?
            Math.ceil((new Date(batch.endDate) - new Date(batch.startDate)) / (1000 * 60 * 60 * 24)) : null
    };

    res.status(200).json(
        new ApiResponse(200, {
            ...batch,
            students: studentsWithEnrollment,
            statistics: stats
        }, 'Batch details retrieved successfully')
    );
});

// Transfer student between trainer's batches
const transferStudentBetweenMyBatches = asyncHandler(async (req, res) => {
    const { studentId, fromBatchId, toBatchId, reason } = req.body;
    const trainerId = req.user._id;

    // Validate ObjectIds
    if (!mongoose.Types.ObjectId.isValid(studentId) ||
        !mongoose.Types.ObjectId.isValid(fromBatchId) ||
        !mongoose.Types.ObjectId.isValid(toBatchId)) {
        throw new ApiError(400, 'Invalid ID format');
    }

    // Verify both batches belong to this trainer
    const [fromBatch, toBatch] = await Promise.all([
        Batch.findOne({ _id: fromBatchId, trainerId: trainerId, isActive: true }),
        Batch.findOne({ _id: toBatchId, trainerId: trainerId, isActive: true })
    ]);

    if (!fromBatch || !toBatch) {
        throw new ApiError(404, 'One or both batches not found or not assigned to you');
    }

    // Verify student is in the source batch
    if (!fromBatch.students.includes(studentId)) {
        throw new ApiError(400, 'Student is not in the source batch');
    }

    // Check if target batch has capacity
    if (toBatch.currentStudentCount >= toBatch.maxStudents) {
        throw new ApiError(400, 'Target batch is at maximum capacity');
    }

    // Verify both batches are for the same course
    if (fromBatch.courseId.toString() !== toBatch.courseId.toString()) {
        throw new ApiError(400, 'Cannot transfer student between batches of different courses');
    }

    const session = await mongoose.startSession();

    try {
        await session.withTransaction(async () => {
            // Remove student from source batch
            await Batch.findByIdAndUpdate(
                fromBatchId,
                {
                    $pull: { students: studentId },
                    $inc: { currentStudentCount: -1 }
                },
                { session }
            );

            // Add student to target batch
            await Batch.findByIdAndUpdate(
                toBatchId,
                {
                    $addToSet: { students: studentId },
                    $inc: { currentStudentCount: 1 }
                },
                { session }
            );

            // Update student's batchId
            await User.findByIdAndUpdate(
                studentId,
                { batchId: toBatchId },
                { session }
            );

            // Update course enrollment
            await CourseEnrollment.findOneAndUpdate(
                { courseId: fromBatch.courseId, studentId: studentId },
                {
                    batchId: toBatchId,
                    status: 'transferred',
                    notes: reason || 'Transferred between trainer batches'
                },
                { session }
            );
        });

        // Get student details for response
        const student = await User.findById(studentId, 'name.first name.last email userId');

        logger.info(`Student ${student.userId} transferred from batch ${fromBatch.batchId} to ${toBatch.batchId} by trainer ${req.user.userId}`);

        res.status(200).json(
            new ApiResponse(200, {
                student: {
                    _id: student._id,
                    name: student.name,
                    email: student.email,
                    userId: student.userId
                },
                fromBatch: {
                    _id: fromBatch._id,
                    batchName: fromBatch.batchName,
                    batchId: fromBatch.batchId
                },
                toBatch: {
                    _id: toBatch._id,
                    batchName: toBatch.batchName,
                    batchId: toBatch.batchId
                },
                reason: reason || 'Not specified'
            }, 'Student transferred successfully between batches')
        );

    } catch (error) {
        logger.error('Error transferring student between batches:', error);
        throw new ApiError(500, 'Failed to transfer student between batches');
    } finally {
        await session.endSession();
    }
});

// ================================
// LEGACY BATCH MANAGEMENT (for backward compatibility)
// ================================
const getAssignedBatches = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;
    const { status, page = 1, limit = 10 } = req.query;

    const filter = { trainerId: trainerId };   // <- Use trainerId field here
    if (status) filter.status = status;

    const batches = await Batch.find(filter)
        .populate('courseId', 'title description duration')
        .populate('students', 'firstName lastName email')
        .populate('collegeId', 'collegeName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Batch.countDocuments(filter);

    res.status(200).json(new ApiResponse(200, {
        batches,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total,
            hasNext: page < Math.ceil(total / limit),
            hasPrev: page > 1
        }
    }, 'Assigned batches retrieved successfully'));
});


const getBatchDetails = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Use correct field names in filter: trainerId (not trainer)
    const batch = await Batch.findOne({ _id: batchId, trainerId: trainerId })
        .populate('courseId', 'title description duration syllabus')   // populate courseId, not course
        .populate('students', 'firstName lastName email mobile')
        .populate('collegeId', 'collegeName address');                  // populate collegeId, not college

    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    // Get batch statistics
    const totalMaterials = await CourseMaterial.countDocuments({ batch: batchId });
    const totalAssignments = await Assignment.countDocuments({ batch: batchId });
    const totalAssessments = await Assessment.countDocuments({ batch: batchId });

    const batchDetails = {
        ...batch.toObject(),
        statistics: {
            totalMaterials,
            totalAssignments,
            totalAssessments,
            totalStudents: batch.students.length
        }
    };

    res.status(200).json(new ApiResponse(200, batchDetails, 'Batch details retrieved successfully'));
});


const getBatchStudents = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId })
        .populate('students', 'firstName lastName email mobile department semester');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    // Get attendance summary for each student
    const studentsWithStats = await Promise.all(
        batch.students.map(async (student) => {
            const totalClasses = await Attendance.countDocuments({ batch: batchId });
            const attendedClasses = await Attendance.countDocuments({
                batch: batchId,
                student: student._id,
                status: true
            });

            const attendancePercentage = totalClasses > 0 ?
                Math.round((attendedClasses / totalClasses) * 100) : 0;

            // Get latest assessment score
            const latestAssessment = await Assessment.findOne({
                batch: batchId,
                'studentMarks.student': student._id
            }).sort({ createdAt: -1 });

            let latestScore = null;
            if (latestAssessment) {
                const studentMark = latestAssessment.studentMarks.find(
                    mark => mark.student.toString() === student._id.toString()
                );
                if (studentMark) {
                    latestScore = {
                        marks: studentMark.marks,
                        maxMarks: latestAssessment.maxMarks,
                        percentage: Math.round((studentMark.marks / latestAssessment.maxMarks) * 100)
                    };
                }
            }

            return {
                ...student.toObject(),
                statistics: {
                    attendancePercentage,
                    totalClasses,
                    attendedClasses,
                    latestScore
                }
            };
        })
    );

    res.status(200).json(new ApiResponse(200, studentsWithStats, 'Batch students retrieved successfully'));
});

// ================================
// COURSE MATERIALS
// ================================
const getBatchMaterials = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    const materials = await CourseMaterial.find({ batch: batchId })
        .populate('uploadedBy', 'firstName lastName')
        .sort({ createdAt: -1 });

    res.status(200).json(new ApiResponse(200, materials, 'Course materials retrieved successfully'));
});

const uploadCourseMaterial = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { title, description, type = 'document' } = req.body;
    const trainerId = req.user._id;

    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    if (!req.file) {
        throw new ApiError(400, 'Please upload a file');
    }

    // Upload to Cloudinary via helper
    const uploadResult = await cloudinaryBufferUpload(
        req.file.buffer,
        'college-management/course-materials'
    );

    const material = await CourseMaterial.create({
        title,
        description,
        type,
        fileUrl: uploadResult.secure_url,
        fileName: req.file.originalname,
        fileSize: req.file.size,
        publicId: uploadResult.public_id,
        batch: batchId,
        uploadedBy: trainerId
    });

    const populatedMaterial = await CourseMaterial.findById(material._id)
        .populate('uploadedBy', 'firstName lastName')
        .populate('batch', 'batchId courseName');

    await Notification.create({
        title: 'New Study Material Uploaded',
        message: `New material "${title}" has been uploaded for ${batch.courseName}`,
        type: 'material',
        recipients: batch.students,
        createdBy: trainerId,
        batch: batchId
    });

    res.status(201).json(
        new ApiResponse(201, populatedMaterial, 'Course material uploaded successfully')
    );
});


const getMaterialDetails = asyncHandler(async (req, res) => {
    const { materialId } = req.params;
    const trainerId = req.user._id;

    const material = await CourseMaterial.findById(materialId)
        .populate('uploadedBy', 'firstName lastName')
        .populate('batch', 'batchId courseName trainerId');

    if (!material) {
        throw new ApiError(404, 'Material not found');
    }

    if (!material.batch?.trainerId || material.batch.trainerId.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to this material');
    }

    res.status(200).json(
        new ApiResponse(200, material, 'Material details retrieved successfully')
    );
});

const updateMaterial = asyncHandler(async (req, res) => {
    const { materialId } = req.params;
    const { title, description } = req.body;
    const trainerId = req.user._id;

    const material = await CourseMaterial.findById(materialId)
        .populate('batch', 'trainer');

    if (!material) {
        throw new ApiError(404, 'Material not found');
    }

    if (material.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to update this material');
    }

    material.title = title || material.title;
    material.description = description || material.description;
    material.updatedAt = new Date();

    await material.save();

    res.status(200).json(new ApiResponse(200, material, 'Material updated successfully'));
});

const deleteMaterial = asyncHandler(async (req, res) => {
    const { materialId } = req.params;
    const trainerId = req.user._id;

    const material = await CourseMaterial.findById(materialId)
        .populate('batch', 'trainer');

    if (!material) {
        throw new ApiError(404, 'Material not found');
    }

    if (material.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to delete this material');
    }

    // Delete file from Cloudinary if publicId exists
    if (material.publicId) {
        const { deleteFromCloudinary } = require('../middleware/upload');
        await deleteFromCloudinary(material.publicId);
    }

    await CourseMaterial.findByIdAndDelete(materialId);

    res.status(200).json(new ApiResponse(200, null, 'Material deleted successfully'));
});

// ================================
// ASSIGNMENTS
// ================================
const getBatchAssignments = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    const assignments = await Assignment.find({ batch: batchId })
        .populate('uploadedBy', 'firstName lastName')
        .sort({ createdAt: -1 });

    // Get submission count for each assignment
    const assignmentsWithStats = await Promise.all(
        assignments.map(async (assignment) => {
            const totalSubmissions = await AssignmentSubmission.countDocuments({
                assignment: assignment._id
            });
            const gradedSubmissions = await AssignmentSubmission.countDocuments({
                assignment: assignment._id,
                isGraded: true
            });

            return {
                ...assignment.toObject(),
                submissionStats: {
                    totalSubmissions,
                    gradedSubmissions,
                    pendingGrading: totalSubmissions - gradedSubmissions
                }
            };
        })
    );

    res.status(200).json(new ApiResponse(200, assignmentsWithStats, 'Assignments retrieved successfully'));
});

const createAssignment = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { title, description, dueDate, maxMarks, instructions, subject } = req.body;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    let assignmentData = {
        title,
        description,
        dueDate: new Date(dueDate),
        maxMarks: parseInt(maxMarks),
        instructions,
        subject,
        batch: batchId,
        uploadedBy: trainerId
    };

    // ✅ Cloudinary upload from memory buffer
    if (req.file) {
        const uploadResult = await cloudinaryBufferUpload(
            req.file.buffer,
            'college-management/assignments'
        );

        assignmentData.fileUrl = uploadResult.secure_url;
        assignmentData.fileName = req.file.originalname;
        assignmentData.fileSize = req.file.size;
        assignmentData.publicId = uploadResult.public_id;
    }

    const assignment = await Assignment.create(assignmentData);

    const populatedAssignment = await Assignment.findById(assignment._id)
        .populate('uploadedBy', 'firstName lastName')
        .populate('batch', 'batchId courseName');

    // Send notification to students
    await Notification.create({
        title: 'New Assignment Posted',
        message: `New assignment "${title}" has been posted. Due date: ${new Date(dueDate).toLocaleDateString()}`,
        type: 'assignment',
        recipients: batch.students,
        createdBy: trainerId,
        batch: batchId,
        relatedId: assignment._id
    });

    res.status(201).json(new ApiResponse(201, populatedAssignment, 'Assignment created successfully'));
});


const getAssignmentDetails = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const trainerId = req.user._id;

    const assignment = await Assignment.findById(assignmentId)
        .populate({
            path: 'batch',
            select: 'batchId courseId trainerId students',
            populate: { path: 'trainerId', select: '_id firstName lastName' }
        })
        .populate('uploadedBy', 'firstName lastName');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    // Defensive check
    if (!assignment.batch || !assignment.batch.trainerId) {
        throw new ApiError(500, 'Assignment batch or trainer not found');
    }

    if (assignment.batch.trainerId._id.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to this assignment');
    }

    const totalStudents = assignment.batch.students.length;

    const submissions = await AssignmentSubmission.find({ assignment: assignmentId })
        .populate('student', 'firstName lastName');

    const submissionStats = {
        totalStudents,
        submitted: submissions.length,
        pending: totalStudents - submissions.length,
        graded: submissions.filter(sub => sub.isGraded).length,
        averageScore: submissions.length > 0
            ? submissions.reduce((sum, sub) => sum + (sub.marks || 0), 0) / submissions.length
            : 0
    };

    res.status(200).json(new ApiResponse(200, {
        assignment,
        submissionStats,
        submissions
    }, 'Assignment details retrieved successfully'));
});


const updateAssignment = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const updateData = req.body;
    const trainerId = req.user._id;

    const assignment = await Assignment.findById(assignmentId)
        .populate('batch', 'trainer');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    if (assignment.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to update this assignment');
    }

    // Update allowed fields
    const allowedFields = ['title', 'description', 'dueDate', 'maxMarks', 'instructions'];
    allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
            assignment[field] = updateData[field];
        }
    });

    assignment.updatedAt = new Date();
    await assignment.save();

    res.status(200).json(new ApiResponse(200, assignment, 'Assignment updated successfully'));
});

const deleteAssignment = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const trainerId = req.user._id;

    const assignment = await Assignment.findById(assignmentId)
        .populate('batch', 'trainer');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    if (assignment.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to delete this assignment');
    }

    // Delete file from Cloudinary if publicId exists
    if (assignment.publicId) {
        const { deleteFromCloudinary } = require('../middleware/upload');
        await deleteFromCloudinary(assignment.publicId);
    }

    // Also delete related submissions
    await AssignmentSubmission.deleteMany({ assignment: assignmentId });
    await Assignment.findByIdAndDelete(assignmentId);

    res.status(200).json(new ApiResponse(200, null, 'Assignment deleted successfully'));
});

const getAssignmentSubmissions = asyncHandler(async (req, res) => {
    const { assignmentId } = req.params;
    const trainerId = req.user._id;
    const { page = 1, limit = 10, status } = req.query;

    const assignment = await Assignment.findById(assignmentId)
        .populate('batch', 'trainer');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    if (assignment.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to this assignment');
    }

    const filter = { assignment: assignmentId };
    if (status === 'graded') filter.isGraded = true;
    if (status === 'pending') filter.isGraded = false;

    const submissions = await AssignmentSubmission.find(filter)
        .populate('student', 'firstName lastName email')
        .sort({ submittedAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await AssignmentSubmission.countDocuments(filter);

    res.status(200).json(new ApiResponse(200, {
        submissions,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Assignment submissions retrieved successfully'));
});

const gradeSubmission = asyncHandler(async (req, res) => {
    const { assignmentId, submissionId } = req.params;
    const { marks, feedback } = req.body;
    const trainerId = req.user._id;

    const assignment = await Assignment.findById(assignmentId)
        .populate('batch', 'trainer');

    if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
    }

    if (assignment.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to grade this assignment');
    }

    const submission = await AssignmentSubmission.findById(submissionId);
    if (!submission) {
        throw new ApiError(404, 'Submission not found');
    }

    if (marks > assignment.maxMarks) {
        throw new ApiError(400, `Marks cannot exceed maximum marks (${assignment.maxMarks})`);
    }

    submission.marks = marks;
    submission.feedback = feedback;
    submission.isGraded = true;
    submission.gradedAt = new Date();
    submission.gradedBy = trainerId;

    await submission.save();

    const populatedSubmission = await AssignmentSubmission.findById(submissionId)
        .populate('student', 'firstName lastName')
        .populate('gradedBy', 'firstName lastName');

    // Send notification to student
    await Notification.create({
        title: 'Assignment Graded',
        message: `Your assignment "${assignment.title}" has been graded. Score: ${marks}/${assignment.maxMarks}`,
        type: 'grade',
        recipients: [submission.student],
        createdBy: trainerId,
        relatedId: submissionId
    });

    res.status(200).json(new ApiResponse(200, populatedSubmission, 'Submission graded successfully'));
});

// ================================
// ATTENDANCE MANAGEMENT
// ================================
const getBatchAttendance = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { date, month, year } = req.query;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainerId: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    let dateFilter = { batchId };

    if (date) {
        const targetDate = new Date(date);
        dateFilter.date = {
            $gte: new Date(targetDate.setHours(0, 0, 0, 0)),
            $lt: new Date(targetDate.setHours(23, 59, 59, 999))
        };
    } else if (month && year) {
        const startDate = new Date(year, month - 1, 1);
        const endDate = new Date(year, month, 0, 23, 59, 59, 999);
        dateFilter.date = { $gte: startDate, $lte: endDate };
    }

    const attendance = await Attendance.find(dateFilter)
        .populate('studentId', 'firstName lastName email')
        .sort({ date: -1, 'studentId.firstName': 1 });

    res.status(200).json(new ApiResponse(200, attendance, 'Attendance records retrieved successfully'));
});
const markAttendance = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { date, studentAttendance } = req.body;
    const trainerId = req.user._id;

    // Validate batchId
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID');
    }

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({
        _id: new mongoose.Types.ObjectId(batchId),
        trainerId: trainerId
    });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    // Validate date
    const attendanceDate = new Date(date);
    if (isNaN(attendanceDate.getTime())) {
        throw new ApiError(400, 'Invalid date format');
    }

    // Insert attendance records (no duplicate date check)
    const attendanceRecords = await Promise.all(
        studentAttendance.map(async ({ studentId, status }) => {
            if (!mongoose.Types.ObjectId.isValid(studentId)) {
                throw new ApiError(400, `Invalid student ID: ${studentId}`);
            }
            return await Attendance.create({
                studentId: new mongoose.Types.ObjectId(studentId),
                batchId: new mongoose.Types.ObjectId(batchId),
                date: attendanceDate,
                status,
                uploadedBy: trainerId
            });
        })
    );

    // Populate student details in the response
    const populatedRecords = await Attendance.find({
        _id: { $in: attendanceRecords.map(record => record._id) }
    }).populate('studentId', 'firstName lastName');

    res.status(201).json(new ApiResponse(201, populatedRecords, 'Attendance marked successfully'));
});
const updateAttendance = asyncHandler(async (req, res) => {
    const { attendanceId } = req.params;
    const { status } = req.body;
    const trainerId = req.user._id;

    const attendance = await Attendance.findById(attendanceId)
        .populate('batch', 'trainer');

    if (!attendance) {
        throw new ApiError(404, 'Attendance record not found');
    }

    if (attendance.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to update this attendance');
    }

    attendance.status = status;
    attendance.updatedAt = new Date();
    await attendance.save();

    res.status(200).json(new ApiResponse(200, attendance, 'Attendance updated successfully'));
});

const getAttendanceSummary = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { startDate, endDate } = req.query;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId })
        .populate('students', 'firstName lastName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    const dateFilter = { batch: batchId };
    if (startDate && endDate) {
        dateFilter.date = {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
        };
    }

    // Get attendance summary for each student
    const attendanceSummary = await Promise.all(
        batch.students.map(async (student) => {
            const totalClasses = await Attendance.countDocuments({
                ...dateFilter,
                student: student._id
            });

            const presentClasses = await Attendance.countDocuments({
                ...dateFilter,
                student: student._id,
                status: true
            });

            const attendancePercentage = totalClasses > 0 ?
                Math.round((presentClasses / totalClasses) * 100) : 0;

            return {
                student: {
                    _id: student._id,
                    firstName: student.firstName,
                    lastName: student.lastName
                },
                totalClasses,
                presentClasses,
                absentClasses: totalClasses - presentClasses,
                attendancePercentage
            };
        })
    );

    res.status(200).json(new ApiResponse(200, attendanceSummary, 'Attendance summary retrieved successfully'));
});

// ================================
// ASSESSMENTS & MARKS
// ================================
const getBatchAssessments = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    const assessments = await Assessment.find({ batch: batchId })
        .populate('uploadedBy', 'firstName lastName')
        .sort({ createdAt: -1 });

    // Add statistics for each assessment
    const assessmentsWithStats = assessments.map(assessment => {
        const totalStudents = assessment.studentMarks.length;
        const averageMarks = totalStudents > 0 ?
            assessment.studentMarks.reduce((sum, mark) => sum + mark.marks, 0) / totalStudents : 0;

        const highestMarks = totalStudents > 0 ?
            Math.max(...assessment.studentMarks.map(mark => mark.marks)) : 0;

        const lowestMarks = totalStudents > 0 ?
            Math.min(...assessment.studentMarks.map(mark => mark.marks)) : 0;

        return {
            ...assessment.toObject(),
            statistics: {
                totalStudents,
                averageMarks: Math.round(averageMarks * 100) / 100,
                highestMarks,
                lowestMarks,
                averagePercentage: Math.round((averageMarks / assessment.maxMarks) * 100)
            }
        };
    });

    res.status(200).json(new ApiResponse(200, assessmentsWithStats, 'Assessments retrieved successfully'));
});

const createAssessment = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { title, type, maxMarks, date, description, subject } = req.body;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    const assessment = await Assessment.create({
        title,
        type,
        maxMarks: parseInt(maxMarks),
        date: new Date(date),
        description,
        subject,
        batch: batchId,
        uploadedBy: trainerId,
        studentMarks: [] // Will be populated when marks are uploaded
    });

    const populatedAssessment = await Assessment.findById(assessment._id)
        .populate('uploadedBy', 'firstName lastName')
        .populate('batch', 'batchId courseName');

    // Send notification to students
    await Notification.create({
        title: 'New Assessment Created',
        message: `New ${type} "${title}" has been created for ${new Date(date).toLocaleDateString()}`,
        type: 'assessment',
        recipients: batch.students,
        createdBy: trainerId,
        batch: batchId,
        relatedId: assessment._id
    });
    res.status(201).json(new ApiResponse(201, populatedAssessment, 'Assessment created successfully'));
});

const getAssessmentDetails = asyncHandler(async (req, res) => {
    const { assessmentId } = req.params;
    const trainerId = req.user._id;

    const assessment = await Assessment.findById(assessmentId)
        .populate('uploadedBy', 'firstName lastName')
        .populate({
            path: 'batch',
            select: 'batchId batchName courseId trainerId',
            populate: {
                path: 'trainerId',
                model: 'User',
                select: '_id firstName lastName email'
            }
        })
        .populate('studentMarks.student', 'firstName lastName email');

    if (!assessment) {
        throw new ApiError(404, 'Assessment not found');
    }

    // Access check using batch.trainerId (populated)
    if (assessment.batch?.trainerId?._id?.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to this assessment');
    }

    res.status(200).json(new ApiResponse(200, assessment, 'Assessment details retrieved successfully'));
});



const updateAssessment = asyncHandler(async (req, res) => {
    const { assessmentId } = req.params;
    const updateData = req.body;
    const trainerId = req.user._id;

    const assessment = await Assessment.findById(assessmentId)
        .populate('batch', 'trainer');

    if (!assessment) {
        throw new ApiError(404, 'Assessment not found');
    }

    if (assessment.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to update this assessment');
    }

    // Update allowed fields
    const allowedFields = ['title', 'type', 'maxMarks', 'date', 'description', 'subject'];
    allowedFields.forEach(field => {
        if (updateData[field] !== undefined) {
            assessment[field] = updateData[field];
        }
    });

    assessment.updatedAt = new Date();
    await assessment.save();

    res.status(200).json(new ApiResponse(200, assessment, 'Assessment updated successfully'));
});

const deleteAssessment = asyncHandler(async (req, res) => {
    const { assessmentId } = req.params;
    const trainerId = req.user._id;

    const assessment = await Assessment.findById(assessmentId)
        .populate('batch', 'trainer');

    if (!assessment) {
        throw new ApiError(404, 'Assessment not found');
    }

    if (assessment.batch.trainer.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to delete this assessment');
    }

    await Assessment.findByIdAndDelete(assessmentId);

    res.status(200).json(new ApiResponse(200, null, 'Assessment deleted successfully'));
});

const uploadAssessmentMarks = asyncHandler(async (req, res) => {
    const { assessmentId } = req.params;
    const { studentMarks } = req.body;
    const trainerId = req.user._id;

    const assessment = await Assessment.findById(assessmentId)
        .populate({
            path: 'batch',
            populate: [
                { path: 'trainerId', select: '_id' },
                { path: 'students', select: '_id' }
            ]
        });

    if (!assessment) {
        throw new ApiError(404, 'Assessment not found');
    }

    if (!assessment.batch?.trainerId || assessment.batch.trainerId._id.toString() !== trainerId.toString()) {
        throw new ApiError(403, 'Access denied to upload marks for this assessment');
    }

    const validStudentIds = assessment.batch.students.map(s => s._id.toString());
    const invalidStudents = studentMarks.filter(mark =>
        !validStudentIds.includes(mark.studentId?.toString())
    );

    if (invalidStudents.length > 0) {
        throw new ApiError(400, 'Some students are not enrolled in this batch');
    }

    const invalidMarks = studentMarks.filter(mark =>
        mark.marks > assessment.maxMarks || mark.marks < 0
    );
    if (invalidMarks.length > 0) {
        throw new ApiError(400, `Marks must be between 0 and ${assessment.maxMarks}`);
    }

    assessment.studentMarks = studentMarks.map(mark => ({
        student: mark.studentId,
        marks: mark.marks,
        remarks: mark.remarks || '',
        grade: mark.grade || ''
    }));

    await assessment.save();

    const populatedAssessment = await Assessment.findById(assessmentId)
        .populate('studentMarks.student', 'firstName lastName');

    const notifications = studentMarks.map(mark => ({
        title: 'Assessment Results Published',
        message: `Results for "${assessment.title}" are now available. Your score: ${mark.marks}/${assessment.maxMarks}`,
        type: 'result',
        recipients: [mark.studentId],
        createdBy: trainerId,
        relatedId: assessmentId
    }));

    await Notification.insertMany(notifications);

    res.status(200).json(
        new ApiResponse(200, populatedAssessment, 'Assessment marks uploaded successfully')
    );
});


// ================================
// NOTIFICATIONS
// ================================
const getNotifications = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;
    const { page = 1, limit = 10, type } = req.query;

    const filter = { createdBy: trainerId };
    if (type) filter.type = type;

    const notifications = await Notification.find(filter)
        .populate('batch', 'batchId courseName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Notification.countDocuments(filter);

    res.status(200).json(new ApiResponse(200, {
        notifications,
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Notifications retrieved successfully'));
});

const sendNotification = asyncHandler(async (req, res) => {
    const { batchId, title, message, type = 'general', recipients } = req.body;
    const trainerId = req.user._id;

    let targetRecipients = [];

    if (batchId) {
        // Verify trainer has access to this batch
        const batch = await Batch.findOne({ _id: batchId, trainer: trainerId });
        if (!batch) {
            throw new ApiError(404, 'Batch not found or not assigned to you');
        }
        targetRecipients = batch.students;
    } else if (recipients && recipients.length > 0) {
        targetRecipients = recipients;
    } else {
        throw new ApiError(400, 'Please specify recipients or batch');
    }

    const notification = await Notification.create({
        title,
        message,
        type,
        recipients: targetRecipients,
        createdBy: trainerId,
        batch: batchId || null
    });

    const populatedNotification = await Notification.findById(notification._id)
        .populate('batch', 'batchId courseName')
        .populate('recipients', 'firstName lastName');

    res.status(201).json(new ApiResponse(201, populatedNotification, 'Notification sent successfully'));
});

const markNotificationAsRead = asyncHandler(async (req, res) => {
    const { notificationId } = req.params;
    const trainerId = req.user._id;

    const notification = await Notification.findOne({
        _id: notificationId,
        recipients: trainerId
    });

    if (!notification) {
        throw new ApiError(404, 'Notification not found');
    }

    // Add trainer to readBy array if not already present
    if (!notification.readBy.includes(trainerId)) {
        notification.readBy.push(trainerId);
        await notification.save();
    }

    res.status(200).json(new ApiResponse(200, null, 'Notification marked as read'));
});

// ================================
// FEEDBACK FROM STUDENTS
// ================================
const getStudentFeedback = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;
    const { batchId, page = 1, limit = 10 } = req.query;

    const filter = { trainer: trainerId };
    if (batchId) filter.batch = batchId;

    const feedback = await Feedback.find(filter)
        .populate('student', 'firstName lastName')
        .populate('batch', 'batchId courseName')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const total = await Feedback.countDocuments(filter);

    // Calculate average rating
    const avgRating = await Feedback.aggregate([
        { $match: filter },
        { $group: { _id: null, averageRating: { $avg: '$rating' } } }
    ]);

    res.status(200).json(new ApiResponse(200, {
        feedback,
        statistics: {
            totalFeedback: total,
            averageRating: avgRating.length > 0 ? Math.round(avgRating[0].averageRating * 10) / 10 : 0
        },
        pagination: {
            currentPage: page,
            totalPages: Math.ceil(total / limit),
            totalItems: total
        }
    }, 'Student feedback retrieved successfully'));
});

const getFeedbackDetails = asyncHandler(async (req, res) => {
    const { feedbackId } = req.params;
    const trainerId = req.user._id;

    const feedback = await Feedback.findOne({
        _id: feedbackId,
        trainer: trainerId
    })
        .populate('student', 'firstName lastName email')
        .populate('batch', 'batchId courseName');

    if (!feedback) {
        throw new ApiError(404, 'Feedback not found');
    }

    res.status(200).json(new ApiResponse(200, feedback, 'Feedback details retrieved successfully'));
});

// ================================
// REPORTS
// ================================
const getBatchReport = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainerId: trainerId })
        .populate('students', 'firstName lastName')
        .populate('courseId', 'title');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    // Get overall statistics
    const totalStudents = batch.students.length;
    const totalMaterials = await CourseMaterial.countDocuments({ batch: batchId });
    const totalAssignments = await Assignment.countDocuments({ batch: batchId });
    const totalAssessments = await Assessment.countDocuments({ batch: batchId });

    // Attendance statistics
    const totalClasses = await Attendance.distinct('date', { batch: batchId }).then(dates => dates.length);
    const avgAttendance = await Attendance.aggregate([
        { $match: { batch: new mongoose.Types.ObjectId(batchId) } },
        { $group: { _id: null, avgAttendance: { $avg: { $cond: ['$status', 1, 0] } } } }
    ]);

    // Assignment submission statistics
    const assignmentStats = await Assignment.aggregate([
        { $match: { batch: new mongoose.Types.ObjectId(batchId) } },
        {
            $lookup: {
                from: 'assignmentsubmissions',
                localField: '_id',
                foreignField: 'assignment',
                as: 'submissions'
            }
        },
        {
            $group: {
                _id: null,
                totalAssignments: { $sum: 1 },
                totalSubmissions: { $sum: { $size: '$submissions' } },
                avgSubmissionRate: { $avg: { $divide: [{ $size: '$submissions' }, totalStudents] } }
            }
        }
    ]);

    // Assessment performance
    const assessmentPerformance = await Assessment.aggregate([
        { $match: { batch: new mongoose.Types.ObjectId(batchId) } },
        { $unwind: '$studentMarks' },
        {
            $group: {
                _id: null,
                avgScore: { $avg: '$studentMarks.marks' },
                totalAssessments: { $sum: 1 }
            }
        }
    ]);

    const report = {
        batch: {
            _id: batch._id,
            batchId: batch.batchId,
            courseName: batch.courseId?.title,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status
        },
        statistics: {
            totalStudents,
            totalMaterials,
            totalAssignments,
            totalAssessments,
            totalClasses,
            averageAttendance: avgAttendance.length > 0 ? Math.round(avgAttendance[0].avgAttendance * 100) : 0,
            assignmentSubmissionRate: assignmentStats.length > 0 ?
                Math.round(assignmentStats[0].avgSubmissionRate * 100) : 0,
            averageAssessmentScore: assessmentPerformance.length > 0 ?
                Math.round(assessmentPerformance[0].avgScore * 100) / 100 : 0
        }
    };

    res.status(200).json(new ApiResponse(200, report, 'Batch report generated successfully'));
});

const getStudentReport = asyncHandler(async (req, res) => {
    const { studentId } = req.params;
    const trainerId = req.user._id;
    const { batchId } = req.query;

    // Verify student is in trainer's batch
    const batch = await Batch.findOne({
        _id: batchId,
        trainer: trainerId,
        students: studentId
    }).populate('students', 'firstName lastName email');

    if (!batch) {
        throw new ApiError(404, 'Student not found in your batches');
    }

    const student = batch.students.find(s => s._id.toString() === studentId);

    // Attendance record
    const attendanceStats = await Attendance.aggregate([
        { $match: { batch: new mongoose.Types.ObjectId(batchId), student: new mongoose.Types.ObjectId(studentId) } },
        {
            $group: {
                _id: null,
                totalClasses: { $sum: 1 },
                presentClasses: { $sum: { $cond: ['$status', 1, 0] } }
            }
        }
    ]);

    // Assignment submissions
    const assignmentSubmissions = await AssignmentSubmission.find({
        student: studentId
    }).populate('assignment', 'title maxMarks batch').then(submissions =>
        submissions.filter(sub => sub.assignment.batch.toString() === batchId)
    );

    // Assessment scores
    const assessmentScores = await Assessment.find({
        batch: batchId,
        'studentMarks.student': studentId
    }).then(assessments =>
        assessments.map(assessment => {
            const studentMark = assessment.studentMarks.find(
                mark => mark.student.toString() === studentId
            );
            return {
                title: assessment.title,
                type: assessment.type,
                marks: studentMark.marks,
                maxMarks: assessment.maxMarks,
                percentage: Math.round((studentMark.marks / assessment.maxMarks) * 100),
                date: assessment.date
            };
        })
    );

    const attendancePercentage = attendanceStats.length > 0 ?
        Math.round((attendanceStats[0].presentClasses / attendanceStats[0].totalClasses) * 100) : 0;

    const avgAssignmentScore = assignmentSubmissions.length > 0 ?
        assignmentSubmissions.reduce((sum, sub) => sum + (sub.marks || 0), 0) / assignmentSubmissions.length : 0;

    const avgAssessmentScore = assessmentScores.length > 0 ?
        assessmentScores.reduce((sum, score) => sum + score.percentage, 0) / assessmentScores.length : 0;

    const report = {
        student: {
            _id: student._id,
            firstName: student.firstName,
            lastName: student.lastName,
            email: student.email
        },
        batch: {
            batchId: batch.batchId,
            courseName: batch.courseName
        },
        performance: {
            attendancePercentage,
            totalClasses: attendanceStats.length > 0 ? attendanceStats[0].totalClasses : 0,
            presentClasses: attendanceStats.length > 0 ? attendanceStats[0].presentClasses : 0,
            assignmentsSubmitted: assignmentSubmissions.length,
            averageAssignmentScore: Math.round(avgAssignmentScore * 100) / 100,
            averageAssessmentScore: Math.round(avgAssessmentScore * 100) / 100
        },
        assessmentScores,
        assignmentSubmissions: assignmentSubmissions.map(sub => ({
            title: sub.assignment.title,
            marks: sub.marks,
            maxMarks: sub.assignment.maxMarks,
            submittedAt: sub.submittedAt,
            isGraded: sub.isGraded
        }))
    };

    res.status(200).json(new ApiResponse(200, report, 'Student report generated successfully'));
});

const getAttendanceReport = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { startDate, endDate } = req.query;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId })
        .populate('students', 'firstName lastName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    const dateFilter = { batch: batchId };
    if (startDate && endDate) {
        dateFilter.date = {
            $gte: new Date(startDate),
            $lte: new Date(endDate)
        };
    }

    // Get daily attendance summary
    const dailyAttendance = await Attendance.aggregate([
        { $match: dateFilter },
        {
            $group: {
                _id: { $dateToString: { format: '%Y-%m-%d', date: '$date' } },
                totalStudents: { $sum: 1 },
                presentStudents: { $sum: { $cond: ['$status', 1, 0] } },
                date: { $first: '$date' }
            }
        },
        { $sort: { date: 1 } }
    ]);

    // Get student-wise attendance
    const studentAttendance = await Promise.all(
        batch.students.map(async (student) => {
            const attendanceRecord = await Attendance.aggregate([
                {
                    $match: {
                        ...dateFilter,
                        student: student._id
                    }
                },
                {
                    $group: {
                        _id: null,
                        totalClasses: { $sum: 1 },
                        presentClasses: { $sum: { $cond: ['$status', 1, 0] } }
                    }
                }
            ]);

            const stats = attendanceRecord.length > 0 ? attendanceRecord[0] : { totalClasses: 0, presentClasses: 0 };

            return {
                student: {
                    _id: student._id,
                    firstName: student.firstName,
                    lastName: student.lastName
                },
                totalClasses: stats.totalClasses,
                presentClasses: stats.presentClasses,
                absentClasses: stats.totalClasses - stats.presentClasses,
                attendancePercentage: stats.totalClasses > 0 ?
                    Math.round((stats.presentClasses / stats.totalClasses) * 100) : 0
            };
        })
    );

    res.status(200).json(new ApiResponse(200, {
        dailyAttendance,
        studentAttendance,
        summary: {
            totalDays: dailyAttendance.length,
            averageAttendance: dailyAttendance.length > 0 ?
                Math.round(dailyAttendance.reduce((sum, day) =>
                    sum + (day.presentStudents / day.totalStudents * 100), 0) / dailyAttendance.length) : 0
        }
    }, 'Attendance report generated successfully'));
});

const getPerformanceReport = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const trainerId = req.user._id;

    // Verify trainer has access to this batch
    const batch = await Batch.findOne({ _id: batchId, trainer: trainerId })
        .populate('students', 'firstName lastName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found or not assigned to you');
    }

    // Get assignment performance
    const assignmentPerformance = await Assignment.aggregate([
        { $match: { batch: new mongoose.Types.ObjectId(batchId) } },
        {
            $lookup: {
                from: 'assignmentsubmissions',
                localField: '_id',
                foreignField: 'assignment',
                as: 'submissions'
            }
        },
        {
            $project: {
                title: 1,
                maxMarks: 1,
                totalSubmissions: { $size: '$submissions' },
                averageMarks: { $avg: '$submissions.marks' },
                submissionRate: {
                    $multiply: [
                        { $divide: [{ $size: '$submissions' }, batch.students.length] },
                        100
                    ]
                }
            }
        }
    ]);

    // Get assessment performance
    const assessmentPerformance = await Assessment.find({ batch: batchId })
        .select('title type maxMarks studentMarks date')
        .then(assessments =>
            assessments.map(assessment => ({
                title: assessment.title,
                type: assessment.type,
                maxMarks: assessment.maxMarks,
                date: assessment.date,
                studentsAppeared: assessment.studentMarks.length,
                averageMarks: assessment.studentMarks.length > 0 ?
                    Math.round((assessment.studentMarks.reduce((sum, mark) => sum + mark.marks, 0) / assessment.studentMarks.length) * 100) / 100 : 0,
                averagePercentage: assessment.studentMarks.length > 0 ?
                    Math.round((assessment.studentMarks.reduce((sum, mark) => sum + mark.marks, 0) / assessment.studentMarks.length / assessment.maxMarks) * 10000) / 100 : 0,
                highestMarks: assessment.studentMarks.length > 0 ?
                    Math.max(...assessment.studentMarks.map(mark => mark.marks)) : 0,
                lowestMarks: assessment.studentMarks.length > 0 ?
                    Math.min(...assessment.studentMarks.map(mark => mark.marks)) : 0
            }))
        );

    // Overall batch statistics
    const overallStats = {
        totalStudents: batch.students.length,
        totalAssignments: assignmentPerformance.length,
        totalAssessments: assessmentPerformance.length,
        averageAssignmentSubmissionRate: assignmentPerformance.length > 0 ?
            Math.round(assignmentPerformance.reduce((sum, assignment) => sum + assignment.submissionRate, 0) / assignmentPerformance.length) : 0,
        averageAssessmentScore: assessmentPerformance.length > 0 ?
            Math.round(assessmentPerformance.reduce((sum, assessment) => sum + assessment.averagePercentage, 0) / assessmentPerformance.length) : 0
    };

    res.status(200).json(new ApiResponse(200, {
        overallStats,
        assignmentPerformance,
        assessmentPerformance
    }, 'Performance report generated successfully'));
});

// ================================
// PROFILE MANAGEMENT
// ================================
const getProfile = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;

    const trainer = await User.findById(trainerId)
        .select('-password')
        .populate('assignedBatches', 'batchId courseName startDate endDate status');

    if (!trainer) {
        throw new ApiError(404, 'Trainer profile not found');
    }

    // Get additional trainer statistics
    const totalStudents = await Batch.aggregate([
        { $match: { trainer: new mongoose.Types.ObjectId(trainerId) } },
        { $group: { _id: null, totalStudents: { $sum: { $size: '$students' } } } }
    ]);

    const totalMaterials = await CourseMaterial.countDocuments({ uploadedBy: trainerId });
    const totalAssignments = await Assignment.countDocuments({ uploadedBy: trainerId });

    const profileData = {
        ...trainer.toObject(),
        statistics: {
            totalBatches: trainer.assignedBatches ? trainer.assignedBatches.length : 0,
            totalStudents: totalStudents.length > 0 ? totalStudents[0].totalStudents : 0,
            totalMaterials,
            totalAssignments
        }
    };

    res.status(200).json(new ApiResponse(200, profileData, 'Profile retrieved successfully'));
});

const updateProfile = asyncHandler(async (req, res) => {
    const trainerId = req.user._id;
    const updateData = req.body;

    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updateData.password;
    delete updateData.role;
    delete updateData.userId;
    delete updateData.assignedBatches;

    const trainer = await User.findByIdAndUpdate(
        trainerId,
        { ...updateData, updatedAt: new Date() },
        { new: true, runValidators: true }
    ).select('-password');

    if (!trainer) {
        throw new ApiError(404, 'Trainer not found');
    }

    res.status(200).json(new ApiResponse(200, trainer, 'Profile updated successfully'));
});

module.exports = {
    getDashboard,
    // Enhanced batch management
    getMyAssignedBatches,
    getMyBatchDetails,
    transferStudentBetweenMyBatches,
    // Legacy batch management
    getAssignedBatches,
    getBatchDetails,
    getBatchStudents,
    getBatchMaterials,
    uploadCourseMaterial,
    getMaterialDetails,
    updateMaterial,
    deleteMaterial,
    getBatchAssignments,
    createAssignment,
    getAssignmentDetails,
    updateAssignment,
    deleteAssignment,
    getAssignmentSubmissions,
    gradeSubmission,
    getBatchAttendance,
    markAttendance,
    updateAttendance,
    getAttendanceSummary,
    getBatchAssessments,
    createAssessment,
    getAssessmentDetails,
    updateAssessment,
    deleteAssessment,
    uploadAssessmentMarks,
    getNotifications,
    sendNotification,
    markNotificationAsRead,
    getStudentFeedback,
    getFeedbackDetails,
    getBatchReport,
    getStudentReport,
    getAttendanceReport,
    getPerformanceReport,
    getProfile,
    updateProfile
};