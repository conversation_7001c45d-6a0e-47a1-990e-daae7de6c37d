const mongoose = require('mongoose');

const courseSchema = new mongoose.Schema({
    title: {
        type: String,
        required: true,
        trim: true
    },
    courseCode: {
        type: String,
        required: true,
        unique: true,
        uppercase: true,
        trim: true,
        validate: {
            validator: function(v) {
                return /^[A-Z0-9]+$/.test(v);
            },
            message: 'Course code must contain only uppercase letters and numbers'
        }
    },
    description: {
        type: String,
        trim: true
    },
    syllabus: {
        type: String,
        trim: true
    },
    duration: {
        type: String, // e.g., "6 weeks", "3 months"
        required: true,
        trim: true
    },
    department: {
        type: String, // Optional: e.g., "CSE", "ECE"
        trim: true
    },
    difficulty: {
        type: String,
        enum: ['beginner', 'intermediate', 'advanced'],
        default: 'intermediate'
    },
    prerequisites: [{
        type: String,
        trim: true
    }],
    objectives: [{
        type: String,
        trim: true
    }],
    modules: [{
        title: {
            type: String,
            required: true,
            trim: true
        },
        description: {
            type: String,
            trim: true
        },
        duration: {
            type: String,
            trim: true
        }
    }],
    materials: [{
        title: {
            type: String,
            required: true,
            trim: true
        },
        type: {
            type: String,
            enum: ['pdf', 'video', 'document', 'link', 'other'],
            default: 'document'
        },
        fileUrl: {
            type: String,
            required: true
        },
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    maxEnrollments: {
        type: Number,
        default: 1000,
        min: [1, 'Maximum enrollments must be at least 1']
    },
    currentEnrollments: {
        type: Number,
        default: 0,
        min: 0
    },
    enrollmentStatus: {
        type: String,
        enum: ['open', 'closed', 'full'],
        default: 'open'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User', // Admin or Trainer
        required: true
    }
}, {
    timestamps: true
});

// Index for better query performance
courseSchema.index({ courseCode: 1 });
courseSchema.index({ department: 1 });
courseSchema.index({ difficulty: 1 });
courseSchema.index({ isActive: 1 });

module.exports = mongoose.model('Course', courseSchema);

