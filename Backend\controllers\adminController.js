const asyncHandler = require('express-async-handler');
const mongoose = require('mongoose');
const ApiError = require('../utils/apiError');
const ApiResponse = require('../utils/apiResponse');
const User = require('../models/User');
const Batch = require('../models/Batch');
const Course = require('../models/Course');
const College = require('../models/College');
const Certificate = require('../models/Certificate');
const CourseEnrollment = require('../models/CourseEnrollment');
const bcrypt = require('bcryptjs');
const { generateUserId } = require('../utils/helpers');
const logger = require('../utils/logger');
const csvParser = require('csv-parser');
const fs = require('fs');
const { sendOtpEmail } = require('../services/emailSender');



//*Cources management
const createCourse = asyncHandler(async (req, res) => {
    const {
        title,
        courseCode,
        description,
        syllabus,
        duration,
        department,
        difficulty,
        prerequisites,
        objectives,
        modules
    } = req.body;

    // Validation
    if (!title || !courseCode || !duration) {
        throw new ApiError(400, 'Please provide all required fields: title, courseCode, and duration');
    }

    // Check if course already exists
    const existingCourse = await Course.findOne({
        courseCode: courseCode.toUpperCase()
    });

    if (existingCourse) {
        throw new ApiError(409, 'Course already exists with this code');
    }

    // Handle file upload if present
    let courseFile = null;
    if (req.file && req.file.path) {
        // Assuming you have a cloudinary upload function
        // const uploaded = await uploadToCloudinary(req.file.path, 'course-materials');
        // courseFile = uploaded.url;
        courseFile = req.file.path; // For now, store file path
    }

    // Create course
    const course = await Course.create({
        title,
        courseCode: courseCode.toUpperCase(),
        description,
        syllabus,
        duration,
        department,
        difficulty: difficulty || 'intermediate',
        prerequisites: prerequisites || [],
        objectives: objectives || [],
        modules: modules || [],
        materials: req.file ? [{
            title: req.file.originalname,
            type: req.file.mimetype.includes('pdf') ? 'pdf' : 'other',
            fileUrl: courseFile,
            uploadedAt: new Date()
        }] : [],
        createdBy: req.user._id
    });

    logger.info(`Course created: ${courseCode} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, course, 'Course created successfully')
    );
});
// ==================== USER MANAGEMENT ====================

const createStudent = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        whatsappNo,
        dateOfBirth,
        studentType, // 'student' or 'professional'
        collegeName,
        companyName,
        passingYear,
        department,
        semester,
        preferredLocation,
        password
    } = req.body;

    if (!firstName || !lastName || !email || !mobile || !dateOfBirth || !studentType) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    // Generate student ID
    const studentId = await generateUserId('STU');

    // Hash password
    const hashedPassword = await bcrypt.hash(password || 'student123', 12);

    //  Generate OTP
    const otp = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit
    const otpExpiry = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // Create student with OTP info
    const student = await User.create({
        userId: studentId,
        name: {
            first: firstName,
            last: lastName
        },
        email: email.toLowerCase(),
        mobile,
        whatsapp: whatsappNo || mobile,
        dob: new Date(dateOfBirth),
        role: 'student',
        isProfessional: studentType === 'professional',
        collegeName: studentType === 'student' ? collegeName : null,
        companyName: studentType === 'professional' ? companyName : null,
        passingYear,
        department,
        semester,
        preferredLocation,
        password: hashedPassword,
        status: 'active',
        otp: {
            code: otp,
            expiresAt: otpExpiry,
            verified: false,
            attempts: 0,
            lastSent: new Date(),
            resendResetTime: new Date(Date.now() + 15 * 60 * 1000) // optional limit window
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: req.user._id
    });

    //  Send OTP Email
    await sendOtpEmail(email, otp);

    // Return success without password
    const { password: _, otp: __, ...studentData } = student.toObject();

    logger.info(`Student created with OTP: ${studentId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, studentData, 'Student created successfully. OTP sent to email.')
    );
});

const createTrainer = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        dateOfBirth,
        qualification,
        experience,
        specialization,
        preferredLocation,
        password
    } = req.body;

    if (!firstName || !lastName || !email || !mobile || !qualification || !experience) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    const trainerId = await generateUserId('TRN');

    const hashedPassword = await bcrypt.hash(password || 'trainer123', 12);

    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = Date.now() + 10 * 60 * 1000; // 10 minutes

    const trainer = await User.create({
        userId: trainerId,
        name: {
            first: firstName,
            last: lastName
        },
        email: email.toLowerCase(),
        mobile,
        dob: new Date(dateOfBirth),
        role: 'trainer',
        qualification,
        experience,
        otherDetails: specialization,
        preferredLocation,
        password: hashedPassword,
        otp: {
            code: otpCode,
            expiresAt: new Date(otpExpiry),
            verified: false,
            attempts: 0
        },
        isActive: true,
        createdBy: req.user._id
    });

    await sendOtpEmail(email, otpCode);

    const { password: _, otp: otpField, ...trainerData } = trainer.toObject();

    logger.info(`Trainer created successfully: ${trainerId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, trainerData, 'Trainer created successfully. OTP sent to email.')
    );
});

const createCollege = asyncHandler(async (req, res) => {
    const {
        firstName,
        lastName,
        email,
        mobile,
        collegeName,
        collegeCode,
        address,
        city,
        state,
        departments,
        password
    } = req.body;

    //  Validation
    if (!firstName || !lastName || !email || !mobile || !collegeName || !collegeCode) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    //  Check if college already exists by collegeCode
    const existingCollege = await College.findOne({
        collegeCode: collegeCode.toUpperCase()
    });


    if (existingCollege) {
        throw new ApiError(409, 'College already exists with this code');
    }

    //  Check if user already exists
    const existingUser = await User.findOne({
        $or: [
            { email: email.toLowerCase() },
            { mobile }
        ]
    });

    if (existingUser) {
        throw new ApiError(409, 'User already exists with this email or mobile number');
    }

    //  Generate College ID
    const collegeId = await generateUserId('COL');

    const hashedPassword = await bcrypt.hash(password || 'college123', 12);

    const otpCode = Math.floor(100000 + Math.random() * 900000).toString();
    const otpExpiry = Date.now() + 10 * 60 * 1000;

    const college = await College.create({
        collegeId,
        collegeName,
        collegeCode: collegeCode.toUpperCase(),
        address,
        city,
        state,
        departments: departments || [],
        isActive: true,
        createdBy: req.user._id
    });

    const collegeUser = await User.create({
        userId: collegeId,
        name: {
            first: firstName,
            last: lastName
        },
        email: email.toLowerCase(),
        mobile,
        role: 'college',
        collegeName,
        collegeCode: collegeCode.toUpperCase(),
        password: hashedPassword,
        otp: {
            code: otpCode,
            expiresAt: new Date(otpExpiry),
            verified: false,
            attempts: 0
        },
        isActive: true,
        createdBy: req.user._id
    });

    await sendOtpEmail(email, otpCode);

    const { password: _, otp, ...collegeUserData } = collegeUser.toObject();

    logger.info(`College created successfully: ${collegeId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, {
            college,
            user: collegeUserData
        }, 'College created successfully. OTP sent to registered email.')
    );
});


const getAllUsers = asyncHandler(async (req, res) => {
    const { role, page = 1, limit = 10, search } = req.query;

    const filter = {};
    if (role) filter.role = role;
    if (search) {
        filter.$or = [
            { firstName: { $regex: search, $options: 'i' } },
            { lastName: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { userId: { $regex: search, $options: 'i' } }
        ];
    }

    const users = await User.find(filter)
        .select('-password')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalUsers = await User.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            users,
            totalPages: Math.ceil(totalUsers / limit),
            currentPage: page,
            totalUsers
        }, 'Users retrieved successfully')
    );
});
const getUserReport = asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const user = await User.findById(userId)
        .select('-password')
        .populate('assignedBatches', 'batchId batchName startDate endDate status');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    let report = {
        user: {
            userId: user._id, // use _id if userId doesn't exist
            fullName: `${user?.name?.first || ''} ${user?.name?.last || ''}`.trim(),
            email: user.email,
            mobile: user.mobile,
            role: user.role,
            isActive: user.isActive,
            createdAt: user.createdAt
        }
    };

    if (user.role === 'student') {
        report.studentDetails = {
            studentType: user.studentType,
            collegeName: user.collegeName,
            companyName: user.companyName,
            department: user.department,
            semester: user.semester,
            passingYear: user.passingYear,
            preferredLocation: user.preferredLocation
        };
    } else if (user.role === 'trainer') {
        report.trainerDetails = {
            qualification: user.qualification,
            experience: user.experience,
            specialization: user.specialization,
            assignedBatches: user.assignedBatches
        };
    } else if (user.role === 'college') {
        report.collegeDetails = {
            collegeName: user.collegeName,
            collegeCode: user.collegeCode
        };
    }

    res.status(200).json(
        new ApiResponse(200, report, 'User report retrieved successfully')
    );
});

const toggleUserStatus = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findOne({ userId });
    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    user.isActive = !user.isActive;
    await user.save();

    res.status(200).json(
        new ApiResponse(200, {
            userId: user.userId,
            isActive: user.isActive
        }, `User ${user.isActive ? 'activated' : 'deactivated'} successfully`)
    );
});

const updateUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;
    const updates = req.body;

    // Remove fields that shouldn't be updated
    delete updates.password;
    delete updates.userId;
    delete updates.role;
    delete updates.createdAt;
    delete updates.updatedAt;

    const user = await User.findOneAndUpdate(
        { userId },
        { ...updates, updatedAt: new Date() },
        { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    res.status(200).json(
        new ApiResponse(200, user, 'User updated successfully')
    );
});


const deleteUser = asyncHandler(async (req, res) => {
    const { userId } = req.params;

    const user = await User.findByIdAndUpdate(
        userId, // Using _id instead of custom field
        {
            isActive: false,
            deletedAt: new Date(),
            deletedBy: req.user._id
        },
        { new: true }
    ).select('-password');

    if (!user) {
        throw new ApiError(404, 'User not found');
    }

    res.status(200).json(
        new ApiResponse(200, user, 'User deleted successfully')
    );
});



const createBatch = asyncHandler(async (req, res) => {
    const {
        batchName,
        courseId,
        trainerId,
        startDate,
        endDate,
        maxStudents,
        location,
        schedule,
        description,
        department
    } = req.body;

    // Validation
    if (!batchName || !courseId || !startDate || !endDate || !maxStudents) {
        throw new ApiError(400, 'Please provide all required fields: batchName, courseId, startDate, endDate, maxStudents');
    }

    // Validate ObjectId format for courseId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Validate ObjectId format for trainerId if provided
    if (trainerId && !mongoose.Types.ObjectId.isValid(trainerId)) {
        throw new ApiError(400, 'Invalid trainer ID format');
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    // Check if course is active
    if (!course.isActive) {
        throw new ApiError(400, 'Cannot create batch for inactive course');
    }

    // Validate trainer if provided
    if (trainerId) {
        const trainer = await User.findOne({ _id: trainerId, role: 'trainer', isActive: true });
        if (!trainer) {
            throw new ApiError(404, 'Trainer not found or inactive');
        }
    }

    // Validate dates
    const batchStartDate = new Date(startDate);
    const batchEndDate = new Date(endDate);

    if (batchStartDate >= batchEndDate) {
        throw new ApiError(400, 'End date must be after start date');
    }

    if (batchStartDate < new Date()) {
        throw new ApiError(400, 'Start date cannot be in the past');
    }

    // Check for duplicate batch name
    const existingBatch = await Batch.findOne({ batchName: batchName.trim() });
    if (existingBatch) {
        throw new ApiError(409, 'Batch with this name already exists');
    }

    // Generate batch ID
    const batchId = await generateUserId('BAT');

    // Create batch
    const batch = await Batch.create({
        batchId,
        batchName: batchName.trim(),
        courseId,
        trainerId: trainerId || null,
        startDate: batchStartDate,
        endDate: batchEndDate,
        maxStudents: parseInt(maxStudents),
        location: location?.trim(),
        schedule: schedule?.trim(),
        description: description?.trim(),
        department: department?.trim() || course.department,
        status: 'upcoming',
        createdBy: req.user._id
    });

    // Populate course and trainer details
    await batch.populate([
        { path: 'courseId', select: 'title courseCode duration department difficulty' },
        { path: 'trainerId', select: 'name.first name.last email userId' }
    ]);

    logger.info(`Batch created successfully: ${batchId} for course: ${course.courseCode} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, batch, 'Batch created successfully')
    );
});

// Get batches by course ID
const getBatchesByCourse = asyncHandler(async (req, res) => {
    const { courseId } = req.params;
    const { page = 1, limit = 10, status } = req.query;

    // Validate courseId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    // Build filter
    const filter = { courseId, isActive: true };
    if (status) filter.status = status;

    const batches = await Batch.find(filter)
        .populate({
            path: 'trainerId',
            select: 'name.first name.last email userId',
            match: { isActive: true }
        })
        .populate({
            path: 'students',
            select: 'name.first name.last email userId',
            match: { isActive: true }
        })
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean();

    const totalBatches = await Batch.countDocuments(filter);

    // Add computed fields
    const enrichedBatches = batches.map(batch => ({
        ...batch,
        currentStudentCount: batch.students ? batch.students.length : 0,
        daysUntilStart: batch.startDate ? Math.ceil((new Date(batch.startDate) - new Date()) / (1000 * 60 * 60 * 24)) : null
    }));

    res.status(200).json(
        new ApiResponse(200, {
            course: {
                _id: course._id,
                title: course.title,
                courseCode: course.courseCode,
                duration: course.duration,
                department: course.department
            },
            batches: enrichedBatches,
            pagination: {
                totalPages: Math.ceil(totalBatches / limit),
                currentPage: parseInt(page),
                totalBatches,
                hasNextPage: page < Math.ceil(totalBatches / limit),
                hasPrevPage: page > 1
            }
        }, 'Batches retrieved successfully for course')
    );
});

// Get courses with their batch counts
const getCoursesWithBatchCounts = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, department } = req.query;

    // Build filter for courses
    const courseFilter = { isActive: true };
    if (department) courseFilter.department = { $regex: department, $options: 'i' };

    const courses = await Course.aggregate([
        { $match: courseFilter },
        {
            $lookup: {
                from: 'batches',
                localField: '_id',
                foreignField: 'courseId',
                as: 'batches',
                pipeline: [
                    { $match: { isActive: true } },
                    { $project: { _id: 1, batchName: 1, status: 1, startDate: 1, endDate: 1, currentStudentCount: 1 } }
                ]
            }
        },
        {
            $addFields: {
                totalBatches: { $size: '$batches' },
                upcomingBatches: {
                    $size: {
                        $filter: {
                            input: '$batches',
                            cond: { $eq: ['$$this.status', 'upcoming'] }
                        }
                    }
                },
                ongoingBatches: {
                    $size: {
                        $filter: {
                            input: '$batches',
                            cond: { $eq: ['$$this.status', 'ongoing'] }
                        }
                    }
                },
                completedBatches: {
                    $size: {
                        $filter: {
                            input: '$batches',
                            cond: { $eq: ['$$this.status', 'completed'] }
                        }
                    }
                }
            }
        },
        { $sort: { createdAt: -1 } },
        { $skip: (page - 1) * limit },
        { $limit: limit * 1 }
    ]);

    const totalCourses = await Course.countDocuments(courseFilter);

    res.status(200).json(
        new ApiResponse(200, {
            courses,
            pagination: {
                totalPages: Math.ceil(totalCourses / limit),
                currentPage: parseInt(page),
                totalCourses,
                hasNextPage: page < Math.ceil(totalCourses / limit),
                hasPrevPage: page > 1
            }
        }, 'Courses with batch information retrieved successfully')
    );
});

// Course Division API - Split course into multiple batches
const divideCourseIntoBatches = asyncHandler(async (req, res) => {
    const { courseId } = req.params;
    const {
        batchConfigurations,
        distributionMethod = 'sequential' // 'sequential', 'random', 'balanced'
    } = req.body;

    let autoAssignStudents = req.body.autoAssignStudents !== undefined ? req.body.autoAssignStudents : true;

    // Validate courseId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Validate batch configurations
    if (!Array.isArray(batchConfigurations) || batchConfigurations.length === 0) {
        throw new ApiError(400, 'Batch configurations are required');
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    if (!course.isActive) {
        throw new ApiError(400, 'Cannot divide inactive course');
    }

    // Get enrolled students who are not yet assigned to batches
    logger.info(`Checking for unassigned students in course: ${courseId}`);
    const unassignedStudents = await CourseEnrollment.getUnassignedStudents(courseId);
    logger.info(`Found ${unassignedStudents.length} unassigned students`);

    // Also check total enrollments for debugging
    const totalEnrollments = await CourseEnrollment.find({ courseId, isActive: true });
    logger.info(`Total enrollments for course: ${totalEnrollments.length}`);

    // If no students are enrolled, we can still create batches but won't assign students
    if (unassignedStudents.length === 0 && autoAssignStudents) {
        logger.info(`No unassigned students found for course ${courseId}. Creating batches without student assignment.`);
        // Set autoAssignStudents to false since there are no students to assign
        autoAssignStudents = false;
    }

    // Validate batch configurations only if we have students to assign
    if (autoAssignStudents && unassignedStudents.length > 0) {
        const totalCapacity = batchConfigurations.reduce((sum, config) => sum + config.maxStudents, 0);
        if (totalCapacity < unassignedStudents.length) {
            throw new ApiError(400, `Total batch capacity (${totalCapacity}) is less than unassigned students (${unassignedStudents.length})`);
        }
    }

    // Validate trainers if provided
    for (const config of batchConfigurations) {
        if (config.trainerId) {
            if (!mongoose.Types.ObjectId.isValid(config.trainerId)) {
                throw new ApiError(400, `Invalid trainer ID format: ${config.trainerId}`);
            }

            const trainer = await User.findOne({
                _id: config.trainerId,
                role: 'trainer',
                isActive: true
            });

            if (!trainer) {
                throw new ApiError(404, `Trainer not found: ${config.trainerId}`);
            }
        }
    }

    const createdBatches = [];
    const session = await mongoose.startSession();

    try {
        logger.info('Starting course division transaction');
        await session.withTransaction(async () => {
            logger.info(`Creating ${batchConfigurations.length} batches`);
            // Create batches
            for (let i = 0; i < batchConfigurations.length; i++) {
                const config = batchConfigurations[i];
                logger.info(`Creating batch ${i + 1}: ${config.batchName}`);

                // Generate batch ID
                const batchId = await generateUserId('BAT');
                logger.info(`Generated batch ID: ${batchId}`);

                // Create batch
                const batch = await Batch.create([{
                    batchId,
                    batchName: config.batchName || `${course.title} - Batch ${i + 1}`,
                    courseId: courseId,
                    trainerId: config.trainerId || null,
                    startDate: new Date(config.startDate),
                    endDate: new Date(config.endDate),
                    maxStudents: config.maxStudents,
                    location: config.location || null,
                    schedule: config.schedule || null,
                    description: config.description || `Auto-generated batch for ${course.title}`,
                    department: config.department || course.department,
                    status: 'upcoming',
                    createdBy: req.user._id
                }], { session });

                logger.info(`Batch created successfully: ${batch[0]._id}`);
                createdBatches.push(batch[0]);

                // Update trainer's assigned batches if trainer is assigned
                if (config.trainerId) {
                    logger.info(`Assigning batch to trainer: ${config.trainerId}`);
                    await User.findByIdAndUpdate(
                        config.trainerId,
                        { $addToSet: { assignedBatches: batch[0]._id } },
                        { session }
                    );
                    logger.info(`Trainer assignment completed for batch: ${batch[0]._id}`);
                }
            }

            // Auto-assign students if requested and students are available
            if (autoAssignStudents && unassignedStudents.length > 0) {
                let studentIndex = 0;
                const studentsToAssign = [...unassignedStudents];

                // Shuffle students if random distribution
                if (distributionMethod === 'random') {
                    for (let i = studentsToAssign.length - 1; i > 0; i--) {
                        const j = Math.floor(Math.random() * (i + 1));
                        [studentsToAssign[i], studentsToAssign[j]] = [studentsToAssign[j], studentsToAssign[i]];
                    }
                }

                // Distribute students across batches
                for (const student of studentsToAssign) {
                    let targetBatchIndex;

                    switch (distributionMethod) {
                        case 'balanced':
                            // Find batch with least students
                            targetBatchIndex = createdBatches.reduce((minIndex, batch, index) => {
                                return batch.currentStudentCount < createdBatches[minIndex].currentStudentCount ? index : minIndex;
                            }, 0);
                            break;
                        case 'sequential':
                        case 'random':
                        default:
                            targetBatchIndex = studentIndex % createdBatches.length;
                            break;
                    }

                    const targetBatch = createdBatches[targetBatchIndex];

                    // Check if batch has capacity
                    if (targetBatch.currentStudentCount < targetBatch.maxStudents) {
                        // Add student to batch
                        await Batch.findByIdAndUpdate(
                            targetBatch._id,
                            {
                                $addToSet: { students: student.studentId._id },
                                $inc: { currentStudentCount: 1 }
                            },
                            { session }
                        );

                        // Update course enrollment
                        await CourseEnrollment.findByIdAndUpdate(
                            student._id,
                            {
                                batchId: targetBatch._id,
                                status: 'assigned_to_batch',
                                assignedToBatchDate: new Date()
                            },
                            { session }
                        );

                        // Update student's batchId
                        await User.findByIdAndUpdate(
                            student.studentId._id,
                            { batchId: targetBatch._id },
                            { session }
                        );

                        targetBatch.currentStudentCount++;
                    }

                    studentIndex++;
                }
            }
        });

        // Populate created batches with details
        logger.info(`Populating ${createdBatches.length} created batches`);

        const populatedBatches = [];
        for (const batch of createdBatches) {
            const populatedBatch = await Batch.findById(batch._id)
                .populate('courseId', 'title courseCode duration')
                .populate('trainerId', 'name.first name.last email userId')
                .populate('students', 'name.first name.last email userId')
                .lean();
            populatedBatches.push(populatedBatch);
        }

        logger.info(`Course ${course.courseCode} divided into ${createdBatches.length} batches by admin: ${req.user.userId}`);

        res.status(201).json(
            new ApiResponse(201, {
                course: {
                    _id: course._id,
                    title: course.title,
                    courseCode: course.courseCode
                },
                batches: populatedBatches,
                totalStudentsAssigned: autoAssignStudents && unassignedStudents.length > 0 ? unassignedStudents.length : 0,
                unassignedStudents: unassignedStudents.length,
                message: unassignedStudents.length === 0 ?
                    'Batches created successfully. No students were available for assignment.' :
                    'Course divided into batches successfully'
            }, 'Course divided into batches successfully')
        );

    } catch (error) {
        logger.error('Error dividing course into batches:', error);
        logger.error('Error details:', {
            message: error.message,
            stack: error.stack,
            name: error.name
        });
        throw new ApiError(500, `Failed to divide course into batches: ${error.message}`);
    } finally {
        await session.endSession();
    }
});

// Enroll students in a course
const enrollStudentsInCourse = asyncHandler(async (req, res) => {
    const { courseId } = req.params;
    const { studentIds } = req.body;
    // Validate courseId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Validate studentIds
    if (!Array.isArray(studentIds) || studentIds.length === 0) {
        throw new ApiError(400, 'Student IDs array is required');
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    if (!course.isActive) {
        throw new ApiError(400, 'Cannot enroll students in inactive course');
    }

    const enrollmentResults = {
        successful: [],
        failed: [],
        alreadyEnrolled: []
    };

    for (const studentId of studentIds) {
        try {
            // Validate student ID format
            if (!mongoose.Types.ObjectId.isValid(studentId)) {
                enrollmentResults.failed.push({
                    studentId,
                    reason: 'Invalid student ID format'
                });
                continue;
            }

            // Check if student exists
            const student = await User.findOne({
                _id: studentId,
                role: 'student',
                isActive: true
            });

            if (!student) {
                enrollmentResults.failed.push({
                    studentId,
                    reason: 'Student not found or inactive'
                });
                continue;
            }

            // Check if already enrolled
            const existingEnrollment = await CourseEnrollment.findOne({
                courseId,
                studentId,
                isActive: true
            });

            if (existingEnrollment) {
                enrollmentResults.alreadyEnrolled.push({
                    studentId,
                    studentName: `${student.name.first} ${student.name.last}`,
                    enrollmentDate: existingEnrollment.enrollmentDate
                });
                continue;
            }

            // Create enrollment
            const enrollment = await CourseEnrollment.create({
                courseId,
                studentId,
                enrolledBy: req.user._id,
                status: 'enrolled'
            });

            // Update course enrollment count
            await Course.findByIdAndUpdate(courseId, {
                $inc: { currentEnrollments: 1 }
            });

            enrollmentResults.successful.push({
                studentId,
                studentName: `${student.name.first} ${student.name.last}`,
                enrollmentId: enrollment._id,
                enrollmentDate: enrollment.enrollmentDate
            });

        } catch (error) {
            enrollmentResults.failed.push({
                studentId,
                reason: error.message
            });
        }
    }

    logger.info(`Course enrollment completed for ${course.courseCode}: ${enrollmentResults.successful.length} successful, ${enrollmentResults.failed.length} failed`);

    res.status(200).json(
        new ApiResponse(200, enrollmentResults, 'Course enrollment process completed')
    );
});

// Get enrolled students for a course
const getCourseEnrollments = asyncHandler(async (req, res) => {
    const { courseId } = req.params;
    const { status = 'enrolled', page = 1, limit = 10 } = req.query;

    // Validate courseId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Check if course exists
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    const filter = { courseId, isActive: true };
    if (status) filter.status = status;

    const enrollments = await CourseEnrollment.find(filter)
        .populate('studentId', 'name email mobile department preferredLocation')
        .populate('batchId', 'batchName batchId')
        .sort({ enrollmentDate: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean();

    const totalEnrollments = await CourseEnrollment.countDocuments(filter);

    // Get enrollment statistics
    const stats = await CourseEnrollment.getCourseStats(courseId);

    res.status(200).json(
        new ApiResponse(200, {
            course: {
                _id: course._id,
                title: course.title,
                courseCode: course.courseCode,
                currentEnrollments: course.currentEnrollments,
                maxEnrollments: course.maxEnrollments
            },
            enrollments,
            statistics: stats[0] || { totalEnrolled: 0, statusBreakdown: [] },
            pagination: {
                totalPages: Math.ceil(totalEnrollments / limit),
                currentPage: parseInt(page),
                totalEnrollments,
                hasNextPage: page < Math.ceil(totalEnrollments / limit),
                hasPrevPage: page > 1
            }
        }, 'Course enrollments retrieved successfully')
    );
});

const assignBatchToTrainer = asyncHandler(async (req, res) => {
    const { batchId, trainerId } = req.body;

    // Validation
    if (!batchId || !trainerId) {
        throw new ApiError(400, 'Please provide batch ID and trainer ID');
    }

    // Check if batch exists
    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Check if trainer exists
    const trainer = await User.findOne({ _id: trainerId, role: 'trainer' }); // Use _id as per your User model
    if (!trainer) {
        throw new ApiError(404, 'Trainer not found');
    }

    // Check if trainer is already assigned to this batch
    if (batch.trainerId && batch.trainerId.equals(trainer._id)) {
        throw new ApiError(409, 'Trainer is already assigned to this batch');
    }

    // Assign trainer to batch
    batch.trainerId = trainer._id;
    await batch.save();

    // Update trainer's assigned batches
    trainer.assignedBatches = trainer.assignedBatches || [];
    if (!trainer.assignedBatches.includes(batch._id)) {
        trainer.assignedBatches.push(batch._id);
        await trainer.save();
    }

    // Populate trainer details
    await batch.populate('trainerId', 'userId name.first name.last email mobile');
    logger.info(`Trainer ${trainerId} assigned to batch ${batchId} by admin: ${req.user.userId}`);

    res.status(200).json(
        new ApiResponse(200, batch, 'Trainer assigned to batch successfully')
    );
});

// Reassign trainer for a batch
const reassignBatchTrainer = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { newTrainerId, reason } = req.body;

    // Validate required fields
    if (!newTrainerId) {
        throw new ApiError(400, 'New trainer ID is required');
    }

    // Validate ObjectId formats
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    if (!mongoose.Types.ObjectId.isValid(newTrainerId)) {
        throw new ApiError(400, 'Invalid trainer ID format');
    }

    // Find batch
    const batch = await Batch.findById(batchId).populate('trainerId', 'name.first name.last email userId');
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Find new trainer
    const newTrainer = await User.findOne({ _id: newTrainerId, role: 'trainer', isActive: true });
    if (!newTrainer) {
        throw new ApiError(404, 'New trainer not found or inactive');
    }

    // Check if it's the same trainer
    if (batch.trainerId && batch.trainerId._id.toString() === newTrainerId) {
        throw new ApiError(400, 'Batch is already assigned to this trainer');
    }

    const oldTrainer = batch.trainerId;
    const session = await mongoose.startSession();

    try {
        await session.withTransaction(async () => {
            // Remove batch from old trainer if exists
            if (oldTrainer) {
                await User.findByIdAndUpdate(
                    oldTrainer._id,
                    { $pull: { assignedBatches: batch._id } },
                    { session }
                );
            }

            // Assign batch to new trainer
            batch.trainerId = newTrainer._id;
            await batch.save({ session });

            // Add batch to new trainer's assigned batches
            await User.findByIdAndUpdate(
                newTrainer._id,
                { $addToSet: { assignedBatches: batch._id } },
                { session }
            );
        });

        // Populate batch with new trainer details
        await batch.populate('trainerId', 'name.first name.last email userId');

        logger.info(`Batch ${batch.batchId} reassigned from trainer ${oldTrainer?.userId || 'none'} to ${newTrainer.userId}. Reason: ${reason || 'Not specified'}`);

        res.status(200).json(
            new ApiResponse(200, {
                batch,
                oldTrainer: oldTrainer ? {
                    _id: oldTrainer._id,
                    name: oldTrainer.name,
                    email: oldTrainer.email,
                    userId: oldTrainer.userId
                } : null,
                newTrainer: {
                    _id: newTrainer._id,
                    name: newTrainer.name,
                    email: newTrainer.email,
                    userId: newTrainer.userId
                },
                reason: reason || 'Not specified'
            }, 'Batch trainer reassigned successfully')
        );

    } catch (error) {
        logger.error('Error reassigning batch trainer:', error);
        throw new ApiError(500, 'Failed to reassign batch trainer');
    } finally {
        await session.endSession();
    }
});

// Get trainer assignments and workload
const getTrainerAssignments = asyncHandler(async (req, res) => {
    const { trainerId } = req.params;
    const { includeStats = true } = req.query;

    // Validate trainerId
    if (!mongoose.Types.ObjectId.isValid(trainerId)) {
        throw new ApiError(400, 'Invalid trainer ID format');
    }

    // Find trainer
    const trainer = await User.findOne({ _id: trainerId, role: 'trainer', isActive: true });
    if (!trainer) {
        throw new ApiError(404, 'Trainer not found or inactive');
    }

    // Get assigned batches
    const assignedBatches = await Batch.find({
        trainerId: trainerId,
        isActive: true
    })
    .populate('courseId', 'title courseCode duration department')
    .populate('students', 'name.first name.last email')
    .sort({ startDate: 1 })
    .lean();

    let stats = null;
    if (includeStats === 'true') {
        // Calculate statistics
        const totalBatches = assignedBatches.length;
        const activeBatches = assignedBatches.filter(batch => batch.status === 'ongoing').length;
        const upcomingBatches = assignedBatches.filter(batch => batch.status === 'upcoming').length;
        const completedBatches = assignedBatches.filter(batch => batch.status === 'completed').length;
        const totalStudents = assignedBatches.reduce((sum, batch) => sum + (batch.students?.length || 0), 0);

        stats = {
            totalBatches,
            activeBatches,
            upcomingBatches,
            completedBatches,
            totalStudents,
            averageStudentsPerBatch: totalBatches > 0 ? Math.round(totalStudents / totalBatches) : 0
        };
    }

    res.status(200).json(
        new ApiResponse(200, {
            trainer: {
                _id: trainer._id,
                name: trainer.name,
                email: trainer.email,
                userId: trainer.userId,
                qualification: trainer.qualification,
                experience: trainer.experience
            },
            assignedBatches,
            statistics: stats
        }, 'Trainer assignments retrieved successfully')
    );
});

// Transfer student between any batches (admin only)
const transferStudentBetweenBatches = asyncHandler(async (req, res) => {
    const { studentId, fromBatchId, toBatchId, reason } = req.body;

    // Validate ObjectIds
    if (!mongoose.Types.ObjectId.isValid(studentId) ||
        !mongoose.Types.ObjectId.isValid(fromBatchId) ||
        !mongoose.Types.ObjectId.isValid(toBatchId)) {
        throw new ApiError(400, 'Invalid ID format');
    }

    // Get both batches
    const [fromBatch, toBatch] = await Promise.all([
        Batch.findById(fromBatchId),
        Batch.findById(toBatchId)
    ]);

    if (!fromBatch || !toBatch) {
        throw new ApiError(404, 'One or both batches not found');
    }

    // Verify student is in the source batch
    if (!fromBatch.students.includes(studentId)) {
        throw new ApiError(400, 'Student is not in the source batch');
    }

    // Check if target batch has capacity
    if (toBatch.currentStudentCount >= toBatch.maxStudents) {
        throw new ApiError(400, 'Target batch is at maximum capacity');
    }

    const session = await mongoose.startSession();

    try {
        await session.withTransaction(async () => {
            // Remove student from source batch
            await Batch.findByIdAndUpdate(
                fromBatchId,
                {
                    $pull: { students: studentId },
                    $inc: { currentStudentCount: -1 }
                },
                { session }
            );

            // Add student to target batch
            await Batch.findByIdAndUpdate(
                toBatchId,
                {
                    $addToSet: { students: studentId },
                    $inc: { currentStudentCount: 1 }
                },
                { session }
            );

            // Update student's batchId
            await User.findByIdAndUpdate(
                studentId,
                { batchId: toBatchId },
                { session }
            );

            // Update course enrollment if both batches are for the same course
            if (fromBatch.courseId.toString() === toBatch.courseId.toString()) {
                await CourseEnrollment.findOneAndUpdate(
                    { courseId: fromBatch.courseId, studentId: studentId },
                    {
                        batchId: toBatchId,
                        status: 'transferred',
                        notes: reason || 'Transferred between batches by admin'
                    },
                    { session }
                );
            } else {
                // Different courses - create new enrollment and mark old as transferred
                await CourseEnrollment.findOneAndUpdate(
                    { courseId: fromBatch.courseId, studentId: studentId },
                    {
                        status: 'transferred',
                        notes: `Transferred to different course: ${reason || 'Admin transfer'}`
                    },
                    { session }
                );

                await CourseEnrollment.create([{
                    courseId: toBatch.courseId,
                    studentId: studentId,
                    batchId: toBatchId,
                    status: 'assigned_to_batch',
                    enrolledBy: req.user._id,
                    notes: `Transferred from different course: ${reason || 'Admin transfer'}`
                }], { session });
            }
        });

        // Get student details for response
        const student = await User.findById(studentId, 'name.first name.last email userId');

        logger.info(`Student ${student.userId} transferred from batch ${fromBatch.batchId} to ${toBatch.batchId} by admin ${req.user.userId}`);

        res.status(200).json(
            new ApiResponse(200, {
                student: {
                    _id: student._id,
                    name: student.name,
                    email: student.email,
                    userId: student.userId
                },
                fromBatch: {
                    _id: fromBatch._id,
                    batchName: fromBatch.batchName,
                    batchId: fromBatch.batchId,
                    courseId: fromBatch.courseId
                },
                toBatch: {
                    _id: toBatch._id,
                    batchName: toBatch.batchName,
                    batchId: toBatch.batchId,
                    courseId: toBatch.courseId
                },
                reason: reason || 'Not specified',
                crossCourseTransfer: fromBatch.courseId.toString() !== toBatch.courseId.toString()
            }, 'Student transferred successfully between batches')
        );

    } catch (error) {
        logger.error('Error transferring student between batches:', error);
        throw new ApiError(500, 'Failed to transfer student between batches');
    } finally {
        await session.endSession();
    }
});

// Bulk transfer students between batches
const bulkTransferStudents = asyncHandler(async (req, res) => {
    const { studentIds, fromBatchId, toBatchId, reason } = req.body;

    // Validate inputs
    if (!Array.isArray(studentIds) || studentIds.length === 0) {
        throw new ApiError(400, 'Student IDs array is required');
    }

    if (!mongoose.Types.ObjectId.isValid(fromBatchId) || !mongoose.Types.ObjectId.isValid(toBatchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    // Get both batches
    const [fromBatch, toBatch] = await Promise.all([
        Batch.findById(fromBatchId),
        Batch.findById(toBatchId)
    ]);

    if (!fromBatch || !toBatch) {
        throw new ApiError(404, 'One or both batches not found');
    }

    // Check if target batch has enough capacity
    const availableCapacity = toBatch.maxStudents - toBatch.currentStudentCount;
    if (availableCapacity < studentIds.length) {
        throw new ApiError(400, `Target batch has only ${availableCapacity} available slots, but ${studentIds.length} students need to be transferred`);
    }

    const transferResults = {
        successful: [],
        failed: []
    };

    const session = await mongoose.startSession();

    try {
        await session.withTransaction(async () => {
            for (const studentId of studentIds) {
                try {
                    // Validate student ID
                    if (!mongoose.Types.ObjectId.isValid(studentId)) {
                        transferResults.failed.push({
                            studentId,
                            reason: 'Invalid student ID format'
                        });
                        continue;
                    }

                    // Verify student is in the source batch
                    if (!fromBatch.students.includes(studentId)) {
                        transferResults.failed.push({
                            studentId,
                            reason: 'Student is not in the source batch'
                        });
                        continue;
                    }

                    // Remove student from source batch
                    await Batch.findByIdAndUpdate(
                        fromBatchId,
                        {
                            $pull: { students: studentId },
                            $inc: { currentStudentCount: -1 }
                        },
                        { session }
                    );

                    // Add student to target batch
                    await Batch.findByIdAndUpdate(
                        toBatchId,
                        {
                            $addToSet: { students: studentId },
                            $inc: { currentStudentCount: 1 }
                        },
                        { session }
                    );

                    // Update student's batchId
                    await User.findByIdAndUpdate(
                        studentId,
                        { batchId: toBatchId },
                        { session }
                    );

                    // Update course enrollment
                    if (fromBatch.courseId.toString() === toBatch.courseId.toString()) {
                        await CourseEnrollment.findOneAndUpdate(
                            { courseId: fromBatch.courseId, studentId: studentId },
                            {
                                batchId: toBatchId,
                                status: 'transferred',
                                notes: reason || 'Bulk transfer by admin'
                            },
                            { session }
                        );
                    }

                    transferResults.successful.push({ studentId });

                } catch (error) {
                    transferResults.failed.push({
                        studentId,
                        reason: error.message
                    });
                }
            }
        });

        logger.info(`Bulk transfer completed: ${transferResults.successful.length} successful, ${transferResults.failed.length} failed`);

        res.status(200).json(
            new ApiResponse(200, {
                transferResults,
                fromBatch: {
                    _id: fromBatch._id,
                    batchName: fromBatch.batchName,
                    batchId: fromBatch.batchId
                },
                toBatch: {
                    _id: toBatch._id,
                    batchName: toBatch.batchName,
                    batchId: toBatch.batchId
                },
                reason: reason || 'Not specified'
            }, 'Bulk student transfer completed')
        );

    } catch (error) {
        logger.error('Error in bulk transfer:', error);
        throw new ApiError(500, 'Failed to complete bulk transfer');
    } finally {
        await session.endSession();
    }
});

// Assign existing batch to course
const assignBatchToCourse = asyncHandler(async (req, res) => {
    const { batchId, courseId } = req.body;

    // Validate required fields
    if (!batchId || !courseId) {
        throw new ApiError(400, 'Batch ID and Course ID are required');
    }

    // Validate ObjectId formats
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Check if batch exists
    const batch = await Batch.findById(batchId);
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    if (!batch.isActive) {
        throw new ApiError(400, 'Cannot assign inactive batch to course');
    }

    // Check if course exists and is active
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    if (!course.isActive) {
        throw new ApiError(400, 'Cannot assign batch to inactive course');
    }

    // Check if batch is already assigned to a different course
    if (batch.courseId && batch.courseId.toString() !== courseId) {
        const currentCourse = await Course.findById(batch.courseId);
        throw new ApiError(400, `Batch is already assigned to course: ${currentCourse?.title || 'Unknown'} (${currentCourse?.courseCode || 'N/A'})`);
    }

    // Check if batch is already assigned to the same course
    if (batch.courseId && batch.courseId.toString() === courseId) {
        throw new ApiError(400, 'Batch is already assigned to this course');
    }

    const session = await mongoose.startSession();

    try {
        await session.withTransaction(async () => {
            // Update batch with course assignment
            await Batch.findByIdAndUpdate(
                batchId,
                {
                    courseId: courseId,
                    department: course.department || batch.department
                },
                { session }
            );

            // Update course enrollment records for students in the batch
            if (batch.students && batch.students.length > 0) {
                for (const studentId of batch.students) {
                    // Check if student already has enrollment for this course
                    const existingEnrollment = await CourseEnrollment.findOne({
                        courseId: courseId,
                        studentId: studentId,
                        isActive: true
                    });

                    if (!existingEnrollment) {
                        // Create new enrollment
                        await CourseEnrollment.create([{
                            courseId: courseId,
                            studentId: studentId,
                            batchId: batchId,
                            status: 'assigned_to_batch',
                            enrolledBy: req.user._id,
                            assignedToBatchDate: new Date(),
                            notes: 'Auto-enrolled when batch was assigned to course'
                        }], { session });
                    } else {
                        // Update existing enrollment
                        await CourseEnrollment.findByIdAndUpdate(
                            existingEnrollment._id,
                            {
                                batchId: batchId,
                                status: 'assigned_to_batch',
                                assignedToBatchDate: new Date(),
                                notes: 'Updated when batch was assigned to course'
                            },
                            { session }
                        );
                    }
                }

                // Update course enrollment count
                await Course.findByIdAndUpdate(
                    courseId,
                    { $inc: { currentEnrollments: batch.students.length } },
                    { session }
                );
            }
        });

        // Populate the updated batch with course and student details
        const updatedBatch = await Batch.findById(batchId)
            .populate('courseId', 'title courseCode duration department difficulty')
            .populate('trainerId', 'name.first name.last email userId')
            .populate('students', 'name.first name.last email userId');

        logger.info(`Batch ${batch.batchId} assigned to course ${course.courseCode} by admin: ${req.user.userId}`);

        res.status(200).json(
            new ApiResponse(200, {
                batch: updatedBatch,
                course: {
                    _id: course._id,
                    title: course.title,
                    courseCode: course.courseCode,
                    department: course.department
                },
                studentsEnrolled: batch.students ? batch.students.length : 0
            }, 'Batch assigned to course successfully')
        );

    } catch (error) {
        logger.error('Error assigning batch to course:', error);
        throw new ApiError(500, 'Failed to assign batch to course');
    } finally {
        await session.endSession();
    }
});

// Create new batch for existing course
const createBatchForCourse = asyncHandler(async (req, res) => {
    const { courseId } = req.params;
    const {
        batchName,
        trainerId,
        startDate,
        endDate,
        maxStudents,
        location,
        schedule,
        description
    } = req.body;

    // Validate courseId
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Validate required fields
    if (!batchName || !startDate || !endDate || !maxStudents) {
        throw new ApiError(400, 'Please provide all required fields: batchName, startDate, endDate, maxStudents');
    }

    // Validate ObjectId format for trainerId if provided
    if (trainerId && !mongoose.Types.ObjectId.isValid(trainerId)) {
        throw new ApiError(400, 'Invalid trainer ID format');
    }

    // Check if course exists and is active
    const course = await Course.findById(courseId);
    if (!course) {
        throw new ApiError(404, 'Course not found');
    }

    if (!course.isActive) {
        throw new ApiError(400, 'Cannot create batch for inactive course');
    }

    // Validate trainer if provided
    if (trainerId) {
        const trainer = await User.findOne({ _id: trainerId, role: 'trainer', isActive: true });
        if (!trainer) {
            throw new ApiError(404, 'Trainer not found or inactive');
        }
    }

    // Validate dates
    const batchStartDate = new Date(startDate);
    const batchEndDate = new Date(endDate);

    if (batchStartDate >= batchEndDate) {
        throw new ApiError(400, 'End date must be after start date');
    }

    if (batchStartDate < new Date()) {
        throw new ApiError(400, 'Start date cannot be in the past');
    }

    // Check for duplicate batch name within the same course
    const existingBatch = await Batch.findOne({
        batchName: batchName.trim(),
        courseId: courseId,
        isActive: true
    });

    if (existingBatch) {
        throw new ApiError(409, 'A batch with this name already exists for this course');
    }

    // Generate batch ID
    const batchId = await generateUserId('BAT');

    const session = await mongoose.startSession();

    try {
        let newBatch;

        await session.withTransaction(async () => {
            // Create batch
            const batchData = {
                batchId,
                batchName: batchName.trim(),
                courseId: courseId,
                trainerId: trainerId || null,
                startDate: batchStartDate,
                endDate: batchEndDate,
                maxStudents: parseInt(maxStudents),
                location: location?.trim(),
                schedule: schedule?.trim(),
                description: description?.trim(),
                department: course.department,
                status: 'upcoming',
                createdBy: req.user._id
            };

            const createdBatch = await Batch.create([batchData], { session });
            newBatch = createdBatch[0];

            // Add batch to trainer's assigned batches if trainer is assigned
            if (trainerId) {
                await User.findByIdAndUpdate(
                    trainerId,
                    { $addToSet: { assignedBatches: newBatch._id } },
                    { session }
                );
            }
        });

        // Populate batch with course and trainer details
        await newBatch.populate([
            { path: 'courseId', select: 'title courseCode duration department difficulty' },
            { path: 'trainerId', select: 'name.first name.last email userId' }
        ]);

        logger.info(`New batch ${batchId} created for course ${course.courseCode} by admin: ${req.user.userId}`);

        res.status(201).json(
            new ApiResponse(201, {
                batch: newBatch,
                course: {
                    _id: course._id,
                    title: course.title,
                    courseCode: course.courseCode,
                    department: course.department
                }
            }, 'Batch created successfully for course')
        );

    } catch (error) {
        logger.error('Error creating batch for course:', error);
        throw new ApiError(500, 'Failed to create batch for course');
    } finally {
        await session.endSession();
    }
});

// Assign students to existing batch
const assignStudentsToBatch = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { studentIds } = req.body;

    // Validate batchId
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    // Validate studentIds
    if (!Array.isArray(studentIds) || studentIds.length === 0) {
        throw new ApiError(400, 'Student IDs array is required and cannot be empty');
    }

    // Validate all student IDs format
    for (const studentId of studentIds) {
        if (!mongoose.Types.ObjectId.isValid(studentId)) {
            throw new ApiError(400, `Invalid student ID format: ${studentId}`);
        }
    }

    // Check if batch exists and is active
    const batch = await Batch.findById(batchId).populate('courseId', 'title courseCode');
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    if (!batch.isActive) {
        throw new ApiError(400, 'Cannot assign students to inactive batch');
    }

    // Check if batch has a course assigned
    if (!batch.courseId) {
        throw new ApiError(400, 'Batch must be assigned to a course before adding students');
    }

    // Check available capacity
    const availableCapacity = batch.maxStudents - batch.currentStudentCount;
    if (availableCapacity < studentIds.length) {
        throw new ApiError(400, `Batch has only ${availableCapacity} available slots, but ${studentIds.length} students need to be assigned`);
    }

    const assignmentResults = {
        successful: [],
        failed: [],
        alreadyAssigned: []
    };

    const session = await mongoose.startSession();

    try {
        await session.withTransaction(async () => {
            for (const studentId of studentIds) {
                try {
                    // Check if student exists and is active
                    const student = await User.findOne({
                        _id: studentId,
                        role: 'student',
                        isActive: true
                    });

                    if (!student) {
                        assignmentResults.failed.push({
                            studentId,
                            studentName: 'Unknown',
                            reason: 'Student not found or inactive'
                        });
                        continue;
                    }

                    // Check if student is already in this batch
                    if (batch.students.includes(studentId)) {
                        assignmentResults.alreadyAssigned.push({
                            studentId,
                            studentName: `${student.name.first} ${student.name.last}`,
                            reason: 'Student is already assigned to this batch'
                        });
                        continue;
                    }

                    // Check if student is already in another batch for the same course
                    const existingBatchForCourse = await Batch.findOne({
                        courseId: batch.courseId._id,
                        students: studentId,
                        isActive: true,
                        _id: { $ne: batchId }
                    });

                    if (existingBatchForCourse) {
                        assignmentResults.failed.push({
                            studentId,
                            studentName: `${student.name.first} ${student.name.last}`,
                            reason: `Student is already assigned to batch: ${existingBatchForCourse.batchName}`
                        });
                        continue;
                    }

                    // Add student to batch
                    await Batch.findByIdAndUpdate(
                        batchId,
                        {
                            $addToSet: { students: studentId },
                            $inc: { currentStudentCount: 1 }
                        },
                        { session }
                    );

                    // Update student's batchId
                    await User.findByIdAndUpdate(
                        studentId,
                        { batchId: batchId },
                        { session }
                    );

                    // Create or update course enrollment
                    const existingEnrollment = await CourseEnrollment.findOne({
                        courseId: batch.courseId._id,
                        studentId: studentId,
                        isActive: true
                    });

                    if (existingEnrollment) {
                        // Update existing enrollment
                        await CourseEnrollment.findByIdAndUpdate(
                            existingEnrollment._id,
                            {
                                batchId: batchId,
                                status: 'assigned_to_batch',
                                assignedToBatchDate: new Date(),
                                notes: 'Assigned to batch by admin'
                            },
                            { session }
                        );
                    } else {
                        // Create new enrollment
                        await CourseEnrollment.create([{
                            courseId: batch.courseId._id,
                            studentId: studentId,
                            batchId: batchId,
                            status: 'assigned_to_batch',
                            enrolledBy: req.user._id,
                            assignedToBatchDate: new Date(),
                            notes: 'Auto-enrolled when assigned to batch'
                        }], { session });

                        // Update course enrollment count
                        await Course.findByIdAndUpdate(
                            batch.courseId._id,
                            { $inc: { currentEnrollments: 1 } },
                            { session }
                        );
                    }

                    assignmentResults.successful.push({
                        studentId,
                        studentName: `${student.name.first} ${student.name.last}`,
                        email: student.email,
                        userId: student.userId
                    });

                } catch (error) {
                    assignmentResults.failed.push({
                        studentId,
                        studentName: 'Unknown',
                        reason: error.message
                    });
                }
            }
        });

        // Get updated batch with populated data
        const updatedBatch = await Batch.findById(batchId)
            .populate('courseId', 'title courseCode duration department')
            .populate('trainerId', 'name.first name.last email userId')
            .populate('students', 'name.first name.last email userId');

        logger.info(`Student assignment to batch ${batch.batchId} completed: ${assignmentResults.successful.length} successful, ${assignmentResults.failed.length} failed, ${assignmentResults.alreadyAssigned.length} already assigned`);

        res.status(200).json(
            new ApiResponse(200, {
                batch: {
                    _id: updatedBatch._id,
                    batchName: updatedBatch.batchName,
                    batchId: updatedBatch.batchId,
                    currentStudentCount: updatedBatch.currentStudentCount,
                    maxStudents: updatedBatch.maxStudents,
                    course: updatedBatch.courseId
                },
                assignmentResults,
                summary: {
                    totalProcessed: studentIds.length,
                    successful: assignmentResults.successful.length,
                    failed: assignmentResults.failed.length,
                    alreadyAssigned: assignmentResults.alreadyAssigned.length,
                    remainingCapacity: updatedBatch.maxStudents - updatedBatch.currentStudentCount
                }
            }, 'Student assignment to batch completed')
        );

    } catch (error) {
        logger.error('Error assigning students to batch:', error);
        throw new ApiError(500, 'Failed to assign students to batch');
    } finally {
        await session.endSession();
    }
});

const getAllBatches = asyncHandler(async (req, res) => {
    const {
        page = 1,
        limit = 10,
        status,
        courseId,
        trainerId,
        department,
        search
    } = req.query;

    // Build filter object
    const filter = { isActive: true };

    if (status) filter.status = status;
    if (courseId && mongoose.Types.ObjectId.isValid(courseId)) {
        filter.courseId = courseId;
    }
    if (trainerId && mongoose.Types.ObjectId.isValid(trainerId)) {
        filter.trainerId = trainerId;
    }
    if (department) filter.department = { $regex: department, $options: 'i' };

    // Add search functionality
    if (search) {
        filter.$or = [
            { batchName: { $regex: search, $options: 'i' } },
            { batchId: { $regex: search, $options: 'i' } },
            { description: { $regex: search, $options: 'i' } }
        ];
    }

    const batches = await Batch.find(filter)
        .populate({
            path: 'courseId',
            select: 'title courseCode duration department difficulty',
            match: { isActive: true }
        })
        .populate({
            path: 'trainerId',
            select: 'name.first name.last email userId',
            match: { isActive: true }
        })
        .populate({
            path: 'students',
            select: 'name.first name.last email userId',
            match: { isActive: true }
        })
        .populate({
            path: 'createdBy',
            select: 'name.first name.last email userId'
        })
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit)
        .lean(); // Use lean for better performance

    const totalBatches = await Batch.countDocuments(filter);

    // Filter out batches where course was not found (inactive courses)
    const activeBatches = batches.filter(batch => batch.courseId);

    // Add computed fields
    const enrichedBatches = activeBatches.map(batch => ({
        ...batch,
        currentStudentCount: batch.students ? batch.students.length : 0,
        isUpcoming: batch.status === 'upcoming',
        isOngoing: batch.status === 'ongoing',
        isCompleted: batch.status === 'completed',
        daysUntilStart: batch.startDate ? Math.ceil((new Date(batch.startDate) - new Date()) / (1000 * 60 * 60 * 24)) : null,
        duration: batch.endDate && batch.startDate ?
            Math.ceil((new Date(batch.endDate) - new Date(batch.startDate)) / (1000 * 60 * 60 * 24)) : null
    }));

    res.status(200).json(
        new ApiResponse(200, {
            batches: enrichedBatches,
            pagination: {
                totalPages: Math.ceil(totalBatches / limit),
                currentPage: parseInt(page),
                totalBatches,
                hasNextPage: page < Math.ceil(totalBatches / limit),
                hasPrevPage: page > 1
            }
        }, 'Batches retrieved successfully')
    );
});


const getBatchReport = asyncHandler(async (req, res) => {
    const { batchId } = req.params;

    // Find batch with populated data
    const batch = await Batch.findOne({ batchId })
        .populate('course', 'courseName courseCode duration')
        .populate('trainers', 'userId firstName lastName email mobile')
        .populate('students', 'userId firstName lastName email mobile department collegeName');

    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    // Get additional statistics
    const totalStudents = batch.students.length;
    const maxStudents = batch.maxStudents;
    const occupancyRate = totalStudents > 0 ? ((totalStudents / maxStudents) * 100).toFixed(2) : 0;

    const report = {
        batch: {
            batchId: batch.batchId,
            batchName: batch.batchName,
            startDate: batch.startDate,
            endDate: batch.endDate,
            status: batch.status,
            location: batch.location,
            schedule: batch.schedule
        },
        course: batch.course,
        trainers: batch.trainers,
        students: batch.students,
        statistics: {
            totalStudents,
            maxStudents,
            occupancyRate: `${occupancyRate}%`,
            availableSlots: maxStudents - totalStudents
        }
    };

    res.status(200).json(
        new ApiResponse(200, report, 'Batch report retrieved successfully')
    );
});


const updateBatchStatus = asyncHandler(async (req, res) => {
    const { batchId } = req.params;
    const { status } = req.body;

    const validStatuses = ['active', 'inactive', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
        throw new ApiError(400, 'Invalid status. Valid statuses: ' + validStatuses.join(', '));
    }

    const batch = await Batch.findOneAndUpdate(
        { batchId },
        { status, updatedAt: new Date() },
        { new: true }
    ).populate('course', 'courseName courseCode');

    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    res.status(200).json(
        new ApiResponse(200, batch, 'Batch status updated successfully')
    );
});


const removeStudentFromBatch = asyncHandler(async (req, res) => {
    const { batchId, studentId } = req.params;

    const batch = await Batch.findOne({ batchId });
    if (!batch) {
        throw new ApiError(404, 'Batch not found');
    }

    const student = await User.findOne({ userId: studentId });
    if (!student) {
        throw new ApiError(404, 'Student not found');
    }

    // Remove student from batch
    batch.students = batch.students.filter(
        id => !id.equals(student._id)
    );
    await batch.save();

    // Remove batch from student's assigned batches
    student.assignedBatches = student.assignedBatches.filter(
        id => !id.equals(batch._id)
    );
    await student.save();

    res.status(200).json(
        new ApiResponse(200, {
            batchId: batch.batchId,
            studentId: student.userId,
            remainingStudents: batch.students.length
        }, 'Student removed from batch successfully')
    );
});

// ==================== COURSE MANAGEMENT ====================


const uploadCourse = asyncHandler(async (req, res) => {
    const {
        courseName,
        courseCode,
        description,
        duration,
        difficulty,
        prerequisites,
        objectives,
        modules
    } = req.body;

    if (!courseName || !courseCode || !duration) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    const existingCourse = await Course.findOne({
        courseCode: courseCode.toUpperCase()
    });

    if (existingCourse) {
        throw new ApiError(409, 'Course already exists with this code');
    }

    let courseFile = null;
    if (req.file && req.file.path) {
        const uploaded = await uploadToCloudinary(req.file.path, 'course-materials');
        courseFile = uploaded.url;
    }

    const course = await Course.create({
        courseName,
        courseCode: courseCode.toUpperCase(),
        description,
        duration,
        difficulty: difficulty || 'intermediate',
        prerequisites: prerequisites || [],
        objectives: objectives || [],
        modules: modules || [],
        courseFile,
        isActive: true,
        createdBy: req.user._id
    });

    logger.info(`Course created: ${courseCode} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, course, 'Course created successfully')
    );
});


const getAllCourses = asyncHandler(async (req, res) => {
    const { page = 1, limit = 10, search } = req.query;

    const filter = {};
    if (search) {
        filter.$or = [
            { courseName: { $regex: search, $options: 'i' } },
            { courseCode: { $regex: search, $options: 'i' } }
        ];
    }

    const courses = await Course.find(filter)
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);

    const totalCourses = await Course.countDocuments(filter);

    res.status(200).json(
        new ApiResponse(200, {
            courses,
            totalPages: Math.ceil(totalCourses / limit),
            currentPage: page,
            totalCourses
        }, 'Courses retrieved successfully')
    );
});

// ==================== CERTIFICATE MANAGEMENT ====================



const uploadCertificate = asyncHandler(async (req, res) => {
    const {
        studentId,
        batchId,
        courseId,
        certificateType,
        issueDate,
        grade,
        remarks
    } = req.body;

    // Validate required fields
    if (!studentId || !batchId || !courseId || !certificateType) {
        throw new ApiError(400, 'Please provide all required fields');
    }

    // Validate existence
    const student = await User.findOne({ userId: studentId, role: 'student' });
    if (!student) throw new ApiError(404, 'Student not found');

    const batch = await Batch.findOne({ batchId });
    if (!batch) throw new ApiError(404, 'Batch not found');

    const course = await Course.findById(courseId);
    if (!course) throw new ApiError(404, 'Course not found');

    const existingCertificate = await Certificate.findOne({
        student: student._id,
        batch: batch._id,
        course: courseId
    });

    if (existingCertificate) {
        throw new ApiError(409, 'Certificate already exists for this student and batch');
    }

    // Upload file to Cloudinary if present
    let certificateFile = null;
    if (req.file && req.file.path) {
        const uploaded = await uploadToCloudinary(req.file.path, 'certificates');
        certificateFile = uploaded.url;
    }

    const certificateId = await generateUserId('CRT');

    const certificate = await Certificate.create({
        certificateId,
        student: student._id,
        batch: batch._id,
        course: courseId,
        certificateType,
        issueDate: new Date(issueDate || Date.now()),
        grade,
        remarks,
        certificateFile,
        status: 'issued',
        issuedBy: req.user._id
    });

    await certificate.populate([
        { path: 'student', select: 'userId firstName lastName email' },
        { path: 'batch', select: 'batchId batchName' },
        { path: 'course', select: 'courseName courseCode' }
    ]);

    logger.info(`Certificate issued: ${certificateId} for student: ${studentId} by admin: ${req.user.userId}`);

    res.status(201).json(
        new ApiResponse(201, certificate, 'Certificate uploaded successfully')
    );
});

// ==================== DASHBOARD ROUTES ====================


const getDashboardStats = asyncHandler(async (req, res) => {
    // Get counts for different entities
    const [
        totalUsers,
        totalStudents,
        totalTrainers,
        totalColleges,
        totalBatches,
        totalCourses,
        totalCertificates,
        activeBatches,
        activeStudents,
        activeTrainers
    ] = await Promise.all([
        User.countDocuments(),
        User.countDocuments({ role: 'student' }),
        User.countDocuments({ role: 'trainer' }),
        User.countDocuments({ role: 'college' }),
        Batch.countDocuments(),
        Course.countDocuments(),
        Certificate.countDocuments(),
        Batch.countDocuments({ status: 'active' }),
        User.countDocuments({ role: 'student', isActive: true }),
        User.countDocuments({ role: 'trainer', isActive: true })
    ]);

    // Get recent registrations (last 30 days)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentRegistrations = await User.countDocuments({
        createdAt: { $gte: thirtyDaysAgo }
    });

    // Get completion rate
    const completedBatches = await Batch.countDocuments({ status: 'completed' });
    const completionRate = totalBatches > 0 ? ((completedBatches / totalBatches) * 100).toFixed(2) : 0;

    const stats = {
        overview: {
            totalUsers,
            totalStudents,
            totalTrainers,
            totalColleges,
            totalBatches,
            totalCourses,
            totalCertificates
        },
        active: {
            activeBatches,
            activeStudents,
            activeTrainers
        },
        metrics: {
            recentRegistrations,
            completionRate: `${completionRate}%`,
            averageStudentsPerBatch: totalBatches > 0 ? Math.round(totalStudents / totalBatches) : 0
        }
    };

    res.status(200).json(
        new ApiResponse(200, stats, 'Dashboard statistics retrieved successfully')
    );
});


const getRecentActivities = asyncHandler(async (req, res) => {
    const { limit = 10 } = req.query;

    // Get recent user registrations
    const recentUsers = await User.find({})
        .select('userId firstName lastName role createdAt')
        .sort({ createdAt: -1 })
        .limit(limit / 2);

    // Get recent batch creations
    const recentBatches = await Batch.find({})
        .select('batchId batchName createdAt')
        .populate('course', 'courseName')
        .sort({ createdAt: -1 })
        .limit(limit / 2);

    // Get recent certificates
    const recentCertificates = await Certificate.find({})
        .select('certificateId certificateType issueDate')
        .populate('student', 'userId firstName lastName')
        .sort({ issueDate: -1 })
        .limit(limit / 2);

    // Combine and sort all activities
    const activities = [
        ...recentUsers.map(user => ({
            type: 'user_registration',
            description: `New ${user.role} registered: ${user.firstName} ${user.lastName}`,
            userId: user.userId,
            timestamp: user.createdAt
        })),
        ...recentBatches.map(batch => ({
            type: 'batch_creation',
            description: `New batch created: ${batch.batchName} for ${batch.course.courseName}`,
            batchId: batch.batchId,
            timestamp: batch.createdAt
        })),
        ...recentCertificates.map(cert => ({
            type: 'certificate_issued',
            description: `Certificate issued to ${cert.student.firstName} ${cert.student.lastName}`,
            certificateId: cert.certificateId,
            timestamp: cert.issueDate
        }))
    ].sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, limit);

    res.status(200).json(
        new ApiResponse(200, activities, 'Recent activities retrieved successfully')
    );
});
const bulkCreateStudents = asyncHandler(async (req, res) => {
    if (!req.file) {
        throw new ApiError(400, 'CSV file is required');
    }

    const results = [];
    fs.createReadStream(req.file.path)
        .pipe(csvParser())
        .on('data', (data) => results.push(data))
        .on('end', async () => {
            const created = [];
            for (const row of results) {
                try {
                    const {
                        firstName,
                        lastName,
                        email,
                        mobile,
                        dateOfBirth,
                        studentType,
                        collegeName,
                        companyName,
                        passingYear,
                        department,
                        semester,
                        preferredLocation
                    } = row;

                    const existing = await User.findOne({ $or: [{ email }, { mobile }] });
                    if (existing) continue;

                    const userId = await generateUserId('STU');
                    const hashedPassword = await bcrypt.hash('student123', 12);

                    await User.create({
                        userId,
                        firstName,
                        lastName,
                        email: email.toLowerCase(),
                        mobile,
                        whatsappNo: mobile,
                        dateOfBirth: new Date(dateOfBirth),
                        role: 'student',
                        studentType,
                        collegeName: studentType === 'student' ? collegeName : null,
                        companyName: studentType === 'professional' ? companyName : null,
                        passingYear,
                        department,
                        semester,
                        preferredLocation,
                        password: hashedPassword,
                        isActive: true,
                        createdBy: req.user._id
                    });
                    created.push(email);
                } catch (error) {
                    logger.error('Error in row: ', row, error);
                }
            }
            res.status(201).json(new ApiResponse(201, { created }, 'Bulk students created'));
        });
});

const bulkAssignStudents = asyncHandler(async (req, res) => {
    const { batchId, studentIds } = req.body;
    if (!batchId || !Array.isArray(studentIds)) {
        throw new ApiError(400, 'Batch ID and student IDs are required');
    }

    const batch = await Batch.findOne({ batchId });
    if (!batch) throw new ApiError(404, 'Batch not found');

    let count = 0;
    for (const studentId of studentIds) {
        const student = await User.findOne({ userId: studentId, role: 'student' });
        if (!student) continue;

        if (!batch.students.includes(student._id)) {
            batch.students.push(student._id);
            student.assignedBatches = student.assignedBatches || [];
            student.assignedBatches.push(batch._id);
            await student.save();
            count++;
        }
    }
    await batch.save();

    res.status(200).json(new ApiResponse(200, { assignedCount: count }, 'Students assigned to batch'));
});

const getDepartmentWiseReport = asyncHandler(async (req, res) => {
    const report = await User.aggregate([
        { $match: { role: 'student', department: { $exists: true } } },
        { $group: { _id: '$department', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
    ]);

    res.status(200).json(new ApiResponse(200, report, 'Department wise report'));
});

const getBatchPerformanceReport = asyncHandler(async (req, res) => {
    const certificates = await Certificate.find()
        .populate('batch', 'batchId batchName')
        .populate('student', 'userId firstName lastName')
        .populate('course', 'courseName');

    const report = certificates.map(cert => ({
        certificateId: cert.certificateId,
        batch: cert.batch?.batchName,
        student: `${cert.student?.firstName} ${cert.student?.lastName}`,
        course: cert.course?.courseName,
        grade: cert.grade,
        remarks: cert.remarks,
        issueDate: cert.issueDate
    }));

    res.status(200).json(new ApiResponse(200, report, 'Batch performance report'));
});
/**
 * Monthly Registration Report
 *  GET /api/admin/report/monthly-registrations
 *  Private (Admin only)
 */
const getMonthlyRegistrationReport = asyncHandler(async (req, res) => {
    const report = await User.aggregate([
        {
            $match: {
                createdAt: { $gte: new Date(new Date().getFullYear(), 0, 1) }
            }
        },
        {
            $group: {
                _id: { month: { $month: '$createdAt' } },
                total: { $sum: 1 }
            }
        },
        { $sort: { '_id.month': 1 } }
    ]);

    res.status(200).json(new ApiResponse(200, report, 'Monthly registrations report'));
});

module.exports = {
    createStudent,
    createTrainer,
    createCollege,
    createCourse,
    createBatch,
    divideCourseIntoBatches,
    enrollStudentsInCourse,
    getCourseEnrollments,
    getBatchesByCourse,
    getCoursesWithBatchCounts,
    assignBatchToTrainer,
    assignBatchToCourse,
    createBatchForCourse,
    assignStudentsToBatch,
    reassignBatchTrainer,
    getTrainerAssignments,
    transferStudentBetweenBatches,
    bulkTransferStudents,
    uploadCourse,
    getAllCourses,
    getAllUsers,
    getAllBatches,
    uploadCertificate,
    getDashboardStats,
    getRecentActivities,
    bulkCreateStudents,
    bulkAssignStudents,
    getDepartmentWiseReport,
    getBatchPerformanceReport,
    getMonthlyRegistrationReport,
    getUserReport,
    toggleUserStatus,
    updateUser,
    deleteUser,
    getBatchReport,
    updateBatchStatus,
    removeStudentFromBatch
};