{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:15)\n    at startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:15)\n    at Object.<anonymous> (D:\\techvritti\\Collegemanagement\\Backend\\server.js:71:1)\n    at Module._compile (node:internal/modules/cjs/loader:1241:14)","timestamp":"2025-07-14 14:07:01"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:02"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:04"}
{"level":"error","message":"❌ Database connection attempt 4 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Mongoose connection error: option buffermaxentries is not supported","service":"college-management-system","stack":"MongoParseError: option buffermaxentries is not supported\n    at parseOptions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\connection_string.js:273:15)\n    at new MongoClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\mongo_client.js:48:63)\n    at NativeConnection.createClient (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\drivers\\node-mongodb-native\\connection.js:288:14)\n    at NativeConnection.openUri (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\connection.js:766:34)\n    at Mongoose.connect (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\index.js:416:15)\n    at connectWithRetry (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:128:47)\n    at async connectDB (D:\\techvritti\\Collegemanagement\\Backend\\config\\db.js:246:9)\n    at async startServer (D:\\techvritti\\Collegemanagement\\Backend\\server.js:37:9)","timestamp":"2025-07-14 14:07:08"}
{"level":"error","message":"❌ Database connection attempt 5 failed:","service":"college-management-system","timestamp":"2025-07-14 14:07:16"}
{"level":"error","message":"sendOtpEmail is not a function - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:55:39"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 16:56:18"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:11:30"}
{"level":"error","message":"Invalid OTP - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 17:58:53"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 17:59:01"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:07:13"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 18:08:28"}
{"level":"error","message":"Invalid OTP - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:10:02"}
{"level":"error","message":"Cannot destructure property 'email' of 'req.body' as it is undefined. - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:13:41"}
{"level":"error","message":"User not found - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:14:09"}
{"level":"error","message":"Wait 1 minute before requesting a new OTP - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:18:40"}
{"level":"error","message":"OTP resend limit reached for today. Try again later. - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:28:09"}
{"level":"error","message":"User already exists with this email - POST /api/auth/register - ::1","service":"college-management-system","timestamp":"2025-07-14 18:46:41"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:47:26"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-14 18:47:42"}
{"level":"error","message":"Wait 1 minute before requesting a new OTP - POST /api/auth/resend-otp - ::1","service":"college-management-system","timestamp":"2025-07-14 18:48:55"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:36:41"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required., preferredLocation: `Pune` is not a valid enum value for path `preferredLocation`. - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:38:17"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required. - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:39:21"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 11:40:11"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 12:01:00"}
{"level":"error","message":"OTP not found or user not registered - POST /api/auth/verify-otp - ::1","service":"college-management-system","timestamp":"2025-07-15 12:13:40"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-07-15 12:15:38"}
{"level":"error","message":"generateOtp is not defined - POST /api/admin/create-trainer - ::1","service":"college-management-system","timestamp":"2025-07-15 12:28:26"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-trainer - ::1","service":"college-management-system","timestamp":"2025-07-15 12:32:58"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:27:40"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:27:52"}
{"level":"error","message":"User validation failed: name.last: Path `name.last` is required., name.first: Path `name.first` is required. - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:28:08"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:29:42"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:32:49"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:32:53"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:33:52"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:29"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:37"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:34:49"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:35:03"}
{"level":"error","message":"College already exists with this code or email - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:39:15"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:50:44"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 16:51:57"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:04:12"}
{"level":"error","message":"E11000 duplicate key error collection: test.colleges index: collegeId_1 dup key: { collegeId: \"COL0001\" } - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:09:27"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:12:54"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:14:57"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:21:05"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL', '$options': 'i' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:23:37"}
{"level":"error","message":"Cast to string failed for value \"{ '$regex': '^COL' }\" (type Object) at path \"collegeId\" for model \"College\" - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:40:48"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:44:57"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-07-15 17:47:52"}
{"level":"error","message":"College already exists with this code - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-07-15 17:53:05"}
{"level":"error","message":"Course not found - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:26:32"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:26:42"}
{"level":"error","message":"Please provide all required fields - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:28:00"}
{"level":"error","message":"Course not found - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:29:19"}
{"level":"error","message":"Batch validation failed: department: Path `department` is required., status: `active` is not a valid enum value for path `status`. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:31:03"}
{"level":"error","message":"Batch validation failed: status: `active` is not a valid enum value for path `status`. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:35:38"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - POST /api/admin/create-batch - ::1","service":"college-management-system","timestamp":"2025-07-15 18:36:58"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 10:26:48"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - GET /api/admin/report/user/6875fcfaa9639a2d69b063af - ::1","service":"college-management-system","timestamp":"2025-08-04 10:32:25"}
{"level":"error","message":"Cannot read properties of undefined (reading 'includes') - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:47:14"}
{"level":"error","message":"Cannot read properties of undefined (reading 'includes') - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:49:03"}
{"level":"error","message":"Batch validation failed: batchName: Path `batchName` is required. - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 10:53:41"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 10:56:44"}
{"level":"error","message":"Cannot populate path `trainers` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 11:00:15"}
{"level":"error","message":"Trainer is already assigned to this batch - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 11:00:58"}
{"level":"error","message":"Cannot populate path `users` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 11:01:13"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/admin/report/batch/BAT0003 - ::1","service":"college-management-system","timestamp":"2025-08-04 11:07:57"}
{"level":"error","message":"User already exists with this email or mobile number - POST /api/admin/create-college - ::1","service":"college-management-system","timestamp":"2025-08-04 11:19:06"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:34:05"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:35:36"}
{"level":"error","message":"Cannot populate path `college` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:38:06"}
{"level":"error","message":"Forbidden - You do not have permission to access this resource - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-04 12:40:46"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches - ::1","service":"college-management-system","timestamp":"2025-08-04 12:43:20"}
{"level":"error","message":"Email not verified. Please verify using OTP. - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:49:53"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:53:14"}
{"level":"error","message":"Invalid credentials - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 12:53:21"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/batches/687652713d110f073072eed1 - ::1","service":"college-management-system","timestamp":"2025-08-04 12:55:20"}
{"level":"error","message":"Batch not found or not assigned to you - GET /api/trainers/batches/687652713d110f073072eed1 - ::1","service":"college-management-system","timestamp":"2025-08-04 12:56:25"}
{"level":"error","message":"Cloudinary upload error:","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"error","message":"File upload failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 13:05:50"}
{"level":"error","message":"Cloudinary upload error:","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
{"level":"error","message":"File upload failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 14:18:32"}
{"level":"error","message":"CourseMaterial validation failed: fileUrl: Path `fileUrl` is required. - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 14:28:04"}
{"level":"error","message":"CourseMaterial validation failed: fileUrl: Path `fileUrl` is required. - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-04 14:32:34"}
{"level":"error","message":"Connection to ac-5hrhu9p-shard-00-00.buaikob.mongodb.net:27017 interrupted due to server monitor timeout - POST /api/auth/login - ::1","service":"college-management-system","timestamp":"2025-08-04 14:46:04"}
{"level":"error","message":"Cannot read properties of undefined (reading 'toString') - GET /api/trainers/materials/68907e1c664fad82a2a17226 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:10:54"}
{"level":"error","message":"Cannot read properties of undefined (reading 'toString') - GET /api/trainers/materials/68907d83664fad82a2a1721d - ::1","service":"college-management-system","timestamp":"2025-08-04 15:11:16"}
{"level":"error","message":"Cast to ObjectId failed for value \"BAT0005\" (type string) at path \"_id\" for model \"Batch\" - GET /api/trainers/batches/BAT0005 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:20:16"}
{"level":"error","message":"Cast to ObjectId failed for value \"BAT0005\" (type string) at path \"_id\" for model \"Batch\" - GET /api/trainers/batches/BAT0005 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:22:49"}
{"level":"error","message":"Cannot read properties of undefined (reading 'toString') - GET /api/trainers/assignments/689085c0ac3c17854b4ae438 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:37:17"}
{"level":"error","message":"Cannot populate path `batch.trainer` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/assignments/689085c0ac3c17854b4ae438 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:38:37"}
{"level":"error","message":"Assignment batch or trainer not found - GET /api/trainers/assignments/689085c0ac3c17854b4ae438 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:41:20"}
{"level":"error","message":"Assignment batch or trainer not found - GET /api/trainers/assignments/689085c0ac3c17854b4ae438 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:44:01"}
{"level":"error","message":"Assignment batch or trainer not found - GET /api/trainers/assignments/689085c0ac3c17854b4ae438 - ::1","service":"college-management-system","timestamp":"2025-08-04 15:44:26"}
{"level":"error","message":"Attendance.find is not a function - GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 15:59:56"}
{"level":"error","message":"Validation failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:09:50"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:19:38"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:20:28"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:22:12"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68903cbff4098f3efcdc5869/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:23:57"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68903cbff4098f3efcdc5869/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:27:44"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68903cbff4098f3efcdc5869/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:27:48"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68903cbff4098f3efcdc5869/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:28:41"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68903cbff4098f3efcdc5869/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:30:25"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:31:39"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:33:30"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:36:37"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:41:21"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:44:46"}
{"level":"error","message":"Class constructor ObjectId cannot be invoked without 'new' - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:49:14"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:51:02"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:52:00"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:53:04"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 16:57:53"}
{"level":"error","message":"Cast to date failed for value \"{\n  '$gte': '2025-08-03T18:30:00.000Z',\n  '$lt': '2025-08-04T18:29:59.999Z'\n}\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 17:02:31"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-08-03T18:30:00.000Z, '$lt': 2025-08-04T18:29:59.999Z }\" (type Object) at path \"date\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 17:06:31"}
{"level":"error","message":"Cast to ObjectId failed for value \"{ '$in': [ new ObjectId(\"68909b8fb01130370d37753e\") ] }\" (type Object) at path \"_id\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 17:07:51"}
{"level":"error","message":"Cast to ObjectId failed for value \"{ '$in': [ new ObjectId(\"68909c20815cee3ff14bf80f\") ] }\" (type Object) at path \"_id\" for model \"Attendance\" - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/attendance - ::1","service":"college-management-system","timestamp":"2025-08-04 17:10:16"}
{"level":"error","message":"Validation failed - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/assessments - ::1","service":"college-management-system","timestamp":"2025-08-04 17:22:01"}
{"level":"error","message":"Cannot read properties of undefined (reading 'toString') - GET /api/trainers/assessments/68909f065cd104ebdce5552c - ::1","service":"college-management-system","timestamp":"2025-08-04 17:25:03"}
{"level":"error","message":"Access denied to this assessment - GET /api/trainers/assessments/68909f065cd104ebdce5552c - ::1","service":"college-management-system","timestamp":"2025-08-04 17:26:38"}
{"level":"error","message":"Cannot populate path `batch.trainer` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/assessments/68909f065cd104ebdce5552c - ::1","service":"college-management-system","timestamp":"2025-08-04 17:29:16"}
{"level":"error","message":"Cannot read properties of undefined (reading 'toString') - POST /api/trainers/assessments/68909f065cd104ebdce5552c/marks - ::1","service":"college-management-system","timestamp":"2025-08-04 17:34:08"}
{"level":"error","message":"Access denied to upload marks for this assessment - POST /api/trainers/assessments/68909f065cd104ebdce5552c/marks - ::1","service":"college-management-system","timestamp":"2025-08-04 17:36:26"}
{"level":"error","message":"Cannot populate path `batch.trainer` because it is not in your schema. Set the `strictPopulate` option to false to override. - POST /api/trainers/assessments/68909f065cd104ebdce5552c/marks - ::1","service":"college-management-system","timestamp":"2025-08-04 17:38:53"}
{"level":"error","message":"Some students are not enrolled in this batch - POST /api/trainers/assessments/68909f065cd104ebdce5552c/marks - ::1","service":"college-management-system","timestamp":"2025-08-04 17:40:37"}
{"level":"error","message":"Notification validation failed: type: `result` is not a valid enum value for path `type`. - POST /api/trainers/assessments/68909f065cd104ebdce5552c/marks - ::1","service":"college-management-system","timestamp":"2025-08-04 17:51:21"}
{"level":"error","message":"Cannot populate path `course` because it is not in your schema. Set the `strictPopulate` option to false to override. - GET /api/trainers/reports/batch/68905ed023a8a6a92f6d54a5 - ::1","service":"college-management-system","timestamp":"2025-08-04 18:09:51"}
{"level":"error","message":"Student not found in your batches - GET /api/trainers/reports/student/6875f8277dc22525967ef792 - ::1","service":"college-management-system","timestamp":"2025-08-04 18:22:08"}
{"level":"error","message":"Student not found in your batches - GET /api/trainers/reports/student/6875f8277dc22525967ef792 - ::1","service":"college-management-system","timestamp":"2025-08-04 18:23:35"}
{"level":"error","message":"Student not found in your batches - GET /api/trainers/reports/student/6875f8277dc22525967ef792??batchId=68905ed023a8a6a92f6d54a5 - ::1","service":"college-management-system","timestamp":"2025-08-04 18:27:11"}
{"level":"error","message":"Batch not found or not assigned to you - GET /api/trainers/reports/attendance/6875f8277dc22525967ef792 - ::1","service":"college-management-system","timestamp":"2025-08-04 18:30:57"}
{"level":"error","message":"Course details not found - GET /api/students/batches/68905ed023a8a6a92f6d54a5/course - ::1","service":"college-management-system","timestamp":"2025-08-05 11:14:44"}
{"level":"error","message":"Access denied to this assignment - GET /api/students/assignments/689085c0ac3c17854b4ae438 - ::1","service":"college-management-system","timestamp":"2025-08-05 11:43:40"}
{"level":"error","message":"Cast to ObjectId failed for value \"{ '$in': [ new ObjectId(\"68905ed023a8a6a92f6d54a5\") ] }\" (type Object) at path \"batch\" for model \"Assessment\" - GET /api/students/results - ::1","service":"college-management-system","timestamp":"2025-08-05 11:45:54"}
{"level":"error","message":"Invalid trainer ID, Invalid batch ID, Rating must be between 1 and 5 - POST /api/students/feedback - ::1","service":"college-management-system","timestamp":"2025-08-06 12:41:09"}
{"level":"error","message":"Notification validation failed: type: `feedback` is not a valid enum value for path `type`. - POST /api/students/feedback - ::1","service":"college-management-system","timestamp":"2025-08-06 12:45:51"}
{"level":"error","message":"Feedback already submitted for this trainer and batch - POST /api/students/feedback - ::1","service":"college-management-system","timestamp":"2025-08-06 12:49:58"}
{"level":"error","message":"Batch not found or you are not enrolled - POST /api/students/certificates/request - ::1","service":"college-management-system","timestamp":"2025-08-06 12:55:54"}
{"level":"error","message":"Minimum 75% attendance required for certificate - POST /api/students/certificates/request - ::1","service":"college-management-system","timestamp":"2025-08-06 12:56:15"}
{"level":"error","message":"Minimum 75% attendance required for certificate - POST /api/students/certificates/request - ::1","service":"college-management-system","timestamp":"2025-08-06 12:57:35"}
{"level":"error","message":"Certificate validation failed: completionDate: Path `completionDate` is required., title: Path `title` is required., course: Path `course` is required., certificateId: Path `certificateId` is required., status: `pending` is not a valid enum value for path `status`. - POST /api/students/certificates/request - ::1","service":"college-management-system","timestamp":"2025-08-06 12:58:53"}
{"level":"error","message":"Cast to date failed for value \"{ '$gte': 2025-05-06T09:29:03.753Z }\" (type Object) at path \"date\" for model \"Attendance\" - GET /api/students/reports/attendance - ::1","service":"college-management-system","timestamp":"2025-08-06 14:59:03"}
{"level":"error","message":"Unexpected file field - POST /api/students/assignments/689085c0ac3c17854b4ae438/submit - ::1","service":"college-management-system","timestamp":"2025-08-06 15:04:13"}
{"level":"error","message":"Access denied to submit this assignment - POST /api/students/assignments/689085c0ac3c17854b4ae438/submit - ::1","service":"college-management-system","timestamp":"2025-08-06 15:09:12"}
{"level":"error","message":"Access denied to submit this assignment - POST /api/students/assignments/689085c0ac3c17854b4ae438/submit - ::1","service":"college-management-system","timestamp":"2025-08-06 15:09:44"}
{"level":"error","message":"Notification validation failed: type: `submission` is not a valid enum value for path `type`. - POST /api/students/assignments/689085c0ac3c17854b4ae438/submit - ::1","service":"college-management-system","timestamp":"2025-08-06 15:12:37"}
{"level":"error","message":"Assignment already submitted. Use update endpoint to modify. - POST /api/students/assignments/689085c0ac3c17854b4ae438/submit - ::1","service":"college-management-system","timestamp":"2025-08-06 15:13:50"}
{"level":"error","message":"Cannot destructure property 'firstName' of 'req.body' as it is undefined. - POST /api/admin/create-student - ::1","service":"college-management-system","timestamp":"2025-08-06 18:20:48"}
{"level":"error","message":"Cannot destructure property 'title' of 'req.body' as it is undefined. - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 18:21:09"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:00:42"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:03:05"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:03:21"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:03:24"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:03:32"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:03:40"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:03:43"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:04:10"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-06 19:04:29"}
{"level":"error","message":"Course already exists with this code - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-07 11:37:28"}
{"level":"error","message":"Class constructor ObjectId cannot be invoked without 'new' - GET /api/admin/courses/689442a6c8081bf4778e0c8f/enrollments - ::1","service":"college-management-system","timestamp":"2025-08-07 11:55:08"}
{"level":"error","message":"Class constructor ObjectId cannot be invoked without 'new' - GET /api/admin/courses/689442a6c8081bf4778e0c8f/enrollments - ::1","service":"college-management-system","timestamp":"2025-08-07 11:55:15"}
{"level":"error","message":"Class constructor ObjectId cannot be invoked without 'new' - GET /api/admin/courses/689442a6c8081bf4778e0c8f/enrollments - ::1","service":"college-management-system","timestamp":"2025-08-07 11:55:29"}
{"level":"error","message":"Trainer not found: 64e55a7bb2a9f4b7fc7df1c2 - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:06:28"}
{"code":11000,"index":0,"keyPattern":{"batchId":1},"keyValue":{"batchId":"BAT0007"},"level":"error","message":"Error dividing course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:07:47"}
{"level":"error","message":"Failed to divide course into batches - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:07:47"}
{"level":"error","message":"Total batch capacity (2) is less than unassigned students (3) - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:08:06"}
{"code":11000,"index":0,"keyPattern":{"batchId":1},"keyValue":{"batchId":"BAT0007"},"level":"error","message":"Error dividing course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:08:20"}
{"level":"error","message":"Failed to divide course into batches - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:08:20"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 12:18:14"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 12:18:14"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-08-07 12:18:25"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 12:18:25"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-08-07 12:18:38"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 12:18:38"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 12:19:26"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 12:19:26"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-08-07 12:19:43"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 12:19:43"}
{"code":11000,"index":0,"keyPattern":{"batchId":1},"keyValue":{"batchId":"BAT0007"},"level":"error","message":"Error dividing course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:22:16"}
{"level":"error","message":"Failed to divide course into batches - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:22:16"}
{"code":11000,"index":0,"keyPattern":{"batchId":1},"keyValue":{"batchId":"BAT0007"},"level":"error","message":"Error dividing course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:22:37"}
{"level":"error","message":"Failed to divide course into batches - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:22:37"}
{"code":11000,"index":0,"keyPattern":{"batchId":1},"keyValue":{"batchId":"BAT0007"},"level":"error","message":"Error dividing course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:23:32"}
{"level":"error","message":"Failed to divide course into batches - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:23:32"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 12:25:13"}
{"cause":{"code":"ECONNRESET","errno":-4077,"syscall":"read"},"connectionGeneration":0,"level":"error","message":"<-> Mongoose connection error: read ECONNRESET","service":"college-management-system","stack":"MongoNetworkError: read ECONNRESET\n    at connectionFailureError (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:367:20)\n    at TLSSocket.<anonymous> (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:290:22)\n    at Object.onceWrapper (node:events:629:26)\n    at TLSSocket.emit (node:events:514:28)\n    at emitErrorNT (node:internal/streams/destroy:151:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:116:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)","timestamp":"2025-08-07 12:25:13"}
{"code":11000,"index":0,"keyPattern":{"batchId":1},"keyValue":{"batchId":"BAT0007"},"level":"error","message":"Error dividing course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:29:50"}
{"level":"error","message":"Error details: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }","name":"MongoServerError","service":"college-management-system","stack":"MongoServerError: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" }\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\insert.js:50:33\n    at D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\operations\\command.js:84:64\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","timestamp":"2025-08-07 12:29:50"}
{"level":"error","message":"Failed to divide course into batches: E11000 duplicate key error collection: test.batches index: batchId_1 dup key: { batchId: \"BAT0007\" } - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:29:50"}
{"kind":"ObjectId","level":"error","message":"Error dividing course into batches: Cast to ObjectId failed for value \"{\n  '$in': [\n    new ObjectId(\"68944fa6425d26481b3616b4\"),\n    new ObjectId(\"68944fa7425d26481b3616b8\")\n  ]\n}\" (type Object) at path \"_id\" for model \"Batch\"","path":"_id","reason":{},"service":"college-management-system","stack":"CastError: Cast to ObjectId failed for value \"{\n  '$in': [\n    new ObjectId(\"68944fa6425d26481b3616b4\"),\n    new ObjectId(\"68944fa7425d26481b3616b8\")\n  ]\n}\" (type Object) at path \"_id\" for model \"Batch\"\n    at ObjectId.cast (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schema\\objectid.js:250:11)\n    at SchemaType.applySetters (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1219:12)\n    at SchemaType.castForQuery (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1633:15)\n    at ObjectId.handleSingle (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1563:15)\n    at SchemaType.castForQuery (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1630:20)\n    at cast (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\cast.js:366:39)\n    at Query.cast (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:4927:12)\n    at Query._castConditions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:2237:10)\n    at model.Query._find (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:2263:8)\n    at model.Query.exec (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:4447:28)","stringValue":"\"{\n  '$in': [\n    new ObjectId(\"68944fa6425d26481b3616b4\"),\n    new ObjectId(\"68944fa7425d26481b3616b8\")\n  ]\n}\"","timestamp":"2025-08-07 12:33:06","value":{"$in":["68944fa6425d26481b3616b4","68944fa7425d26481b3616b8"]},"valueType":"Object"}
{"level":"error","message":"Error details: Cast to ObjectId failed for value \"{\n  '$in': [\n    new ObjectId(\"68944fa6425d26481b3616b4\"),\n    new ObjectId(\"68944fa7425d26481b3616b8\")\n  ]\n}\" (type Object) at path \"_id\" for model \"Batch\"","name":"CastError","service":"college-management-system","stack":"CastError: Cast to ObjectId failed for value \"{\n  '$in': [\n    new ObjectId(\"68944fa6425d26481b3616b4\"),\n    new ObjectId(\"68944fa7425d26481b3616b8\")\n  ]\n}\" (type Object) at path \"_id\" for model \"Batch\"\n    at ObjectId.cast (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schema\\objectid.js:250:11)\n    at SchemaType.applySetters (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1219:12)\n    at SchemaType.castForQuery (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1633:15)\n    at ObjectId.handleSingle (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1563:15)\n    at SchemaType.castForQuery (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\schematype.js:1630:20)\n    at cast (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\cast.js:366:39)\n    at Query.cast (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:4927:12)\n    at Query._castConditions (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:2237:10)\n    at model.Query._find (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:2263:8)\n    at model.Query.exec (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongoose\\lib\\query.js:4447:28)","timestamp":"2025-08-07 12:33:06"}
{"level":"error","message":"Failed to divide course into batches: Cast to ObjectId failed for value \"{\n  '$in': [\n    new ObjectId(\"68944fa6425d26481b3616b4\"),\n    new ObjectId(\"68944fa7425d26481b3616b8\")\n  ]\n}\" (type Object) at path \"_id\" for model \"Batch\" - POST /api/admin/courses/689442a6c8081bf4778e0c8f/divide-into-batches - ::1","service":"college-management-system","timestamp":"2025-08-07 12:33:06"}
{"level":"error","message":"Validation failed: Invalid batch ID format - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-07 12:43:37"}
{"level":"error","message":"Batch not found - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-07 12:44:02"}
{"level":"error","message":"Validation failed: Invalid batch ID format - POST /api/admin/assign-batch - ::1","service":"college-management-system","timestamp":"2025-08-07 12:44:15"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 15:20:29"}
{"connectionGeneration":0,"level":"error","message":"<-> Mongoose connection error: connection 2 to 159.41.253.207:27017 closed","service":"college-management-system","stack":"MongoNetworkError: connection 2 to 159.41.253.207:27017 closed\n    at Connection.onClose (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:121:28)\n    at TLSSocket.<anonymous> (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:63:46)\n    at TLSSocket.emit (node:events:514:28)\n    at node:net:337:12\n    at TCP.done (node:_tls_wrap:657:7)","timestamp":"2025-08-07 15:20:29"}
{"level":"error","message":"Validation failed: Course title is required, Course title must be between 3 and 100 characters, Course code is required, Course code must be between 2 and 10 characters, Course code must contain only uppercase letters and numbers, Course duration is required - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-07 15:26:52"}
{"level":"error","message":"Validation failed: Course title is required, Course title must be between 3 and 100 characters, Course code is required, Course code must be between 2 and 10 characters, Course code must contain only uppercase letters and numbers, Course duration is required - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-07 15:30:59"}
{"level":"error","message":"Validation failed: Course title is required, Course title must be between 3 and 100 characters, Course code is required, Course code must be between 2 and 10 characters, Course code must contain only uppercase letters and numbers, Course duration is required - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-07 15:31:00"}
{"level":"error","message":"Validation failed: Course title is required, Course title must be between 3 and 100 characters, Course code is required, Course code must be between 2 and 10 characters, Course code must contain only uppercase letters and numbers, Course duration is required - POST /api/admin/courses/create - ::1","service":"college-management-system","timestamp":"2025-08-07 15:31:00"}
{"level":"error","message":"Access denied. You are not assigned to this batch. - GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/students - ::1","service":"college-management-system","timestamp":"2025-08-07 15:34:15"}
{"level":"error","message":"Access denied. You are not assigned to this batch. - GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/students - ::1","service":"college-management-system","timestamp":"2025-08-07 15:38:47"}
{"level":"error","message":"Batch not found or not assigned to you - GET /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-07 15:39:08"}
{"level":"error","message":"Please upload a file - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-07 15:39:21"}
{"level":"error","message":"Batch not found or not assigned to you - POST /api/trainers/batches/68905ed023a8a6a92f6d54a5/materials - ::1","service":"college-management-system","timestamp":"2025-08-07 15:39:37"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68907d83664fad82a2a1721d - ::1","service":"college-management-system","timestamp":"2025-08-07 15:40:05"}
{"level":"error","message":"Batch not found or not assigned to you - POST /api/trainers/batches/68947b6ef2f63314add6754d/materials - ::1","service":"college-management-system","timestamp":"2025-08-07 15:41:02"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947b6ef2f63314add6754d - ::1","service":"college-management-system","timestamp":"2025-08-07 15:41:19"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947b6ef2f63314add6754d - ::1","service":"college-management-system","timestamp":"2025-08-07 15:41:23"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947b6ef2f63314add6754d - ::1","service":"college-management-system","timestamp":"2025-08-07 15:41:33"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68907a809f1adc72a97a11a2 - ::1","service":"college-management-system","timestamp":"2025-08-07 15:41:50"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947c20f2f63314add67570 - ::1","service":"college-management-system","timestamp":"2025-08-07 15:43:05"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947c20f2f63314add67570 - ::1","service":"college-management-system","timestamp":"2025-08-07 15:44:01"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947d0ef2f63314add67587 - ::1","service":"college-management-system","timestamp":"2025-08-07 15:47:45"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/68947d0ef2f63314add67587 - ::1","service":"college-management-system","timestamp":"2025-08-07 15:48:50"}
{"level":"error","message":"Connection to ac-5hrhu9p-shard-00-02.buaikob.mongodb.net:27017 interrupted due to server monitor timeout - GET /api/trainers/materials/68947d0ef2f63314add67587 - ::1","service":"college-management-system","timestamp":"2025-08-07 15:50:32"}
{"error":{"http_code":499,"message":"Request Timeout","name":"TimeoutError"},"level":"error","message":"Unhandled Promise Rejection:","service":"college-management-system","timestamp":"2025-08-07 15:54:38"}
{"level":"error","message":"Cloudinary upload failed - POST /api/trainers/batches/689450dca827baea8952e931/materials - ::1","service":"college-management-system","timestamp":"2025-08-07 15:54:38"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 15:55:36"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 15:55:36"}
{"error":{"code":"ECONNRESET","errno":-4077,"syscall":"write"},"level":"error","message":"Unhandled Promise Rejection:","service":"college-management-system","timestamp":"2025-08-07 15:58:16"}
{"level":"error","message":"Cloudinary upload failed - POST /api/trainers/batches/689450dca827baea8952e931/materials - ::1","service":"college-management-system","timestamp":"2025-08-07 15:58:16"}
{"level":"error","message":"Access denied to this material - GET /api/trainers/materials/6894801da992ad15be2767f1 - ::1","service":"college-management-system","timestamp":"2025-08-07 16:00:16"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 16:04:07"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:04:07"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-08-07 16:04:32"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:04:32"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-08-07 16:05:01"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:05:01"}
{"level":"error","message":"❌ Database connection attempt 4 failed:","service":"college-management-system","timestamp":"2025-08-07 16:05:11"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:05:11"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 16:05:47"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:05:47"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-08-07 16:06:14"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:06:14"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-08-07 16:06:32"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:06:32"}
{"level":"error","message":"❌ Database connection attempt 1 failed:","service":"college-management-system","timestamp":"2025-08-07 16:12:12"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:12:12"}
{"level":"error","message":"❌ Database connection attempt 2 failed:","service":"college-management-system","timestamp":"2025-08-07 16:12:24"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:12:24"}
{"level":"error","message":"❌ Database connection attempt 3 failed:","service":"college-management-system","timestamp":"2025-08-07 16:12:45"}
{"level":"error","message":"<-> Mongoose connection error: Server selection timed out after 5000 ms","reason":{"commonWireVersion":0,"compatible":true,"heartbeatFrequencyMS":10000,"localThresholdMS":15,"logicalSessionTimeoutMinutes":null,"maxElectionId":null,"maxSetVersion":null,"servers":{},"setName":"atlas-hv4qok-shard-0","stale":false,"type":"ReplicaSetNoPrimary"},"service":"college-management-system","stack":"MongoServerSelectionError: Server selection timed out after 5000 ms\n    at Timeout._onTimeout (D:\\techvritti\\Collegemanagement\\Backend\\node_modules\\mongodb\\lib\\sdam\\topology.js:278:38)\n    at listOnTimeout (node:internal/timers:573:17)\n    at process.processTimers (node:internal/timers:514:7)","timestamp":"2025-08-07 16:12:45"}
{"level":"error","message":"❌ Database connection attempt 4 failed:","service":"college-management-system","timestamp":"2025-08-07 16:13:22"}
{"code":"ETIMEOUT","hostname":"cluster0.buaikob.mongodb.net","level":"error","message":"<-> Mongoose connection error: queryTxt ETIMEOUT cluster0.buaikob.mongodb.net","service":"college-management-system","stack":"Error: queryTxt ETIMEOUT cluster0.buaikob.mongodb.net\n    at QueryReqWrap.onresolve [as oncomplete] (node:internal/dns/promises:251:17)","syscall":"queryTxt","timestamp":"2025-08-07 16:13:22"}
