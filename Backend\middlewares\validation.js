// middlewares/validation.js
const { body, validationResult } = require('express-validator');
const ApiError = require('../utils/ApiError');

// Middleware to handle validation errors
const handleValidationErrors = (req, res, next) => {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
        const errorMessages = errors.array().map(error => error.msg);
        throw new ApiError(400, `Validation failed: ${errorMessages.join(', ')}`);
    }
    next();
};

// Student validation
const validateCreateCourse = [
    body('title')
        .notEmpty()
        .withMessage('Course title is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Course title must be between 3 and 100 characters'),

    body('courseCode')
        .notEmpty()
        .withMessage('Course code is required')
        .isLength({ min: 2, max: 10 })
        .withMessage('Course code must be between 2 and 10 characters')
        .matches(/^[A-Z0-9]+$/)
        .withMessage('Course code must contain only uppercase letters and numbers'),

    body('duration')
        .notEmpty()
        .withMessage('Course duration is required'),

    body('description')
        .optional()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

    body('department')
        .optional()
        .isLength({ max: 50 })
        .withMessage('Department name cannot exceed 50 characters'),

    body('difficulty')
        .optional()
        .isIn(['beginner', 'intermediate', 'advanced'])
        .withMessage('Difficulty must be beginner, intermediate, or advanced'),

    body('prerequisites')
        .optional()
        .isArray()
        .withMessage('Prerequisites must be an array'),

    body('objectives')
        .optional()
        .isArray()
        .withMessage('Objectives must be an array'),

    handleValidationErrors
];

const validateCreateStudent = [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
    body('password').isLength({ min: 6 }).withMessage('Password must be at least 6 characters'),
    handleValidationErrors
];

// Trainer validation
const validateCreateTrainer = [
    body('firstName').notEmpty().withMessage('First name is required'),
    body('lastName').notEmpty().withMessage('Last name is required'),
    body('email').isEmail().withMessage('Valid email is required'),
    handleValidationErrors
];

// College validation
const validateCreateCollege = [
    body('collegeName').notEmpty().withMessage('College name is required'),
    body('location').notEmpty().withMessage('Location is required'),
    handleValidationErrors
];

// Batch validation
const validateCreateBatch = [
    body('batchName')
        .notEmpty()
        .withMessage('Batch name is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Batch name must be between 3 and 100 characters')
        .trim(),

    body('courseId')
        .notEmpty()
        .withMessage('Course ID is required')
        .isMongoId()
        .withMessage('Invalid course ID format'),

    body('trainerId')
        .optional()
        .isMongoId()
        .withMessage('Invalid trainer ID format'),

    body('startDate')
        .notEmpty()
        .withMessage('Start date is required')
        .isISO8601()
        .withMessage('Invalid start date format')
        .custom((value) => {
            const startDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (startDate < today) {
                throw new Error('Start date cannot be in the past');
            }
            return true;
        }),

    body('endDate')
        .notEmpty()
        .withMessage('End date is required')
        .isISO8601()
        .withMessage('Invalid end date format')
        .custom((value, { req }) => {
            const endDate = new Date(value);
            const startDate = new Date(req.body.startDate);
            if (endDate <= startDate) {
                throw new Error('End date must be after start date');
            }
            return true;
        }),

    body('maxStudents')
        .notEmpty()
        .withMessage('Maximum students is required')
        .isInt({ min: 1, max: 500 })
        .withMessage('Maximum students must be between 1 and 500'),

    body('location')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Location cannot exceed 200 characters'),

    body('schedule')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Schedule cannot exceed 100 characters'),

    body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

    body('department')
        .optional()
        .trim()
        .isLength({ max: 50 })
        .withMessage('Department cannot exceed 50 characters'),

    handleValidationErrors
];

// Batch assignment validation
const validateAssignBatch = [
    body('trainerId')
        .notEmpty()
        .withMessage('Trainer ID is required')
        .isMongoId()
        .withMessage('Invalid trainer ID format'),

    body('batchId')
        .notEmpty()
        .withMessage('Batch ID is required')
        .isMongoId()
        .withMessage('Invalid batch ID format'),

    handleValidationErrors
];

// Course upload validation
const validateUploadCourse = [
    body('courseName').notEmpty().withMessage('Course name is required'),
    body('courseCode').notEmpty().withMessage('Course code is required'),
    body('duration').notEmpty().withMessage('Course duration is required'),
    handleValidationErrors
];

// Certificate upload validation
const validateUploadCertificate = [
    body('studentId')
        .notEmpty()
        .withMessage('Student ID is required')
        .isMongoId()
        .withMessage('Invalid student ID format'),

    body('batchId')
        .notEmpty()
        .withMessage('Batch ID is required')
        .isMongoId()
        .withMessage('Invalid batch ID format'),

    body('courseId')
        .notEmpty()
        .withMessage('Course ID is required')
        .isMongoId()
        .withMessage('Invalid course ID format'),

    body('certificateType').notEmpty().withMessage('Certificate type is required'),
    handleValidationErrors
];

// Validation for assigning batch to course
const validateAssignBatchToCourse = [
    body('batchId')
        .notEmpty()
        .withMessage('Batch ID is required')
        .isMongoId()
        .withMessage('Invalid batch ID format'),

    body('courseId')
        .notEmpty()
        .withMessage('Course ID is required')
        .isMongoId()
        .withMessage('Invalid course ID format'),

    handleValidationErrors
];

// Validation for creating batch for course
const validateCreateBatchForCourse = [
    body('batchName')
        .notEmpty()
        .withMessage('Batch name is required')
        .isLength({ min: 3, max: 100 })
        .withMessage('Batch name must be between 3 and 100 characters')
        .trim(),

    body('trainerId')
        .optional()
        .isMongoId()
        .withMessage('Invalid trainer ID format'),

    body('startDate')
        .notEmpty()
        .withMessage('Start date is required')
        .isISO8601()
        .withMessage('Invalid start date format')
        .custom((value) => {
            const startDate = new Date(value);
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            if (startDate < today) {
                throw new Error('Start date cannot be in the past');
            }
            return true;
        }),

    body('endDate')
        .notEmpty()
        .withMessage('End date is required')
        .isISO8601()
        .withMessage('Invalid end date format')
        .custom((value, { req }) => {
            const endDate = new Date(value);
            const startDate = new Date(req.body.startDate);
            if (endDate <= startDate) {
                throw new Error('End date must be after start date');
            }
            return true;
        }),

    body('maxStudents')
        .notEmpty()
        .withMessage('Maximum students is required')
        .isInt({ min: 1, max: 500 })
        .withMessage('Maximum students must be between 1 and 500'),

    body('location')
        .optional()
        .trim()
        .isLength({ max: 200 })
        .withMessage('Location cannot exceed 200 characters'),

    body('schedule')
        .optional()
        .trim()
        .isLength({ max: 100 })
        .withMessage('Schedule cannot exceed 100 characters'),

    body('description')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Description cannot exceed 1000 characters'),

    handleValidationErrors
];

// Validation for assigning students to batch
const validateAssignStudentsToBatch = [
    body('studentIds')
        .isArray({ min: 1 })
        .withMessage('Student IDs array is required and cannot be empty')
        .custom((studentIds) => {
            // Validate each student ID is a valid MongoDB ObjectId
            for (const studentId of studentIds) {
                if (typeof studentId !== 'string' || !studentId.match(/^[0-9a-fA-F]{24}$/)) {
                    throw new Error(`Invalid student ID format: ${studentId}`);
                }
            }
            return true;
        }),

    handleValidationErrors
];

module.exports = {
    validateCreateStudent,
    validateCreateTrainer,
    validateCreateCollege,
    validateCreateBatch,
    validateAssignBatch,
    validateUploadCourse,
    validateUploadCertificate,
    validateCreateCourse,
    validateAssignBatchToCourse,
    validateCreateBatchForCourse,
    validateAssignStudentsToBatch,
    handleValidationErrors
};
