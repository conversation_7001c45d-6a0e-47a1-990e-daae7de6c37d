const asyncHandler = require('../utils/asyncHandler');
const ApiError = require('../utils/ApiError');
const Batch = require('../models/Batch');
const mongoose = require('mongoose');

// Middleware to ensure trainers can only access their assigned batches
const verifyBatchOwnership = asyncHandler(async (req, res, next) => {
    const { batchId } = req.params;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Validate batchId format
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    // Admin can access all batches
    if (userRole === 'admin') {
        return next();
    }

    // For trainers, verify they are assigned to this batch
    if (userRole === 'trainer') {
        const batch = await Batch.findOne({ 
            _id: batchId, 
            trainerId: userId, 
            isActive: true 
        });

        if (!batch) {
            throw new ApiError(403, 'Access denied. You are not assigned to this batch.');
        }

        // Attach batch to request for use in controller
        req.batch = batch;
        return next();
    }

    // For students, verify they are enrolled in this batch
    if (userRole === 'student') {
        const batch = await Batch.findOne({ 
            _id: batchId, 
            students: userId, 
            isActive: true 
        });

        if (!batch) {
            throw new ApiError(403, 'Access denied. You are not enrolled in this batch.');
        }

        // Attach batch to request for use in controller
        req.batch = batch;
        return next();
    }

    // Other roles are not allowed
    throw new ApiError(403, 'Access denied. Insufficient permissions.');
});

// Middleware to ensure trainers can only access batches they are assigned to (batch ID in body)
const verifyBatchOwnershipFromBody = asyncHandler(async (req, res, next) => {
    const { batchId } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Validate batchId format
    if (!mongoose.Types.ObjectId.isValid(batchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    // Admin can access all batches
    if (userRole === 'admin') {
        return next();
    }

    // For trainers, verify they are assigned to this batch
    if (userRole === 'trainer') {
        const batch = await Batch.findOne({ 
            _id: batchId, 
            trainerId: userId, 
            isActive: true 
        });

        if (!batch) {
            throw new ApiError(403, 'Access denied. You are not assigned to this batch.');
        }

        // Attach batch to request for use in controller
        req.batch = batch;
        return next();
    }

    // Other roles are not allowed for this operation
    throw new ApiError(403, 'Access denied. Insufficient permissions.');
});

// Middleware to verify trainer can access multiple batches (for transfer operations)
const verifyMultipleBatchOwnership = asyncHandler(async (req, res, next) => {
    const { fromBatchId, toBatchId } = req.body;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Validate batchId formats
    if (!mongoose.Types.ObjectId.isValid(fromBatchId) || !mongoose.Types.ObjectId.isValid(toBatchId)) {
        throw new ApiError(400, 'Invalid batch ID format');
    }

    // Admin can access all batches
    if (userRole === 'admin') {
        return next();
    }

    // For trainers, verify they are assigned to both batches
    if (userRole === 'trainer') {
        const [fromBatch, toBatch] = await Promise.all([
            Batch.findOne({ _id: fromBatchId, trainerId: userId, isActive: true }),
            Batch.findOne({ _id: toBatchId, trainerId: userId, isActive: true })
        ]);

        if (!fromBatch || !toBatch) {
            throw new ApiError(403, 'Access denied. You are not assigned to one or both batches.');
        }

        // Attach batches to request for use in controller
        req.fromBatch = fromBatch;
        req.toBatch = toBatch;
        return next();
    }

    // Other roles are not allowed for this operation
    throw new ApiError(403, 'Access denied. Insufficient permissions.');
});

// Middleware to ensure only trainers can access trainer-specific endpoints
const requireTrainerRole = asyncHandler(async (req, res, next) => {
    if (req.user.role !== 'trainer') {
        throw new ApiError(403, 'Access denied. Trainer role required.');
    }
    next();
});

// Middleware to ensure only admins or trainers can access batch management endpoints
const requireBatchManagementRole = asyncHandler(async (req, res, next) => {
    const allowedRoles = ['admin', 'trainer'];
    if (!allowedRoles.includes(req.user.role)) {
        throw new ApiError(403, 'Access denied. Admin or Trainer role required.');
    }
    next();
});

// Middleware to verify course ownership for trainers (when creating batches)
const verifyCourseAccess = asyncHandler(async (req, res, next) => {
    const { courseId } = req.body || req.params;
    const userRole = req.user.role;

    // Validate courseId format
    if (!mongoose.Types.ObjectId.isValid(courseId)) {
        throw new ApiError(400, 'Invalid course ID format');
    }

    // Admin can access all courses
    if (userRole === 'admin') {
        return next();
    }

    // For trainers, they can only work with courses where they have assigned batches
    if (userRole === 'trainer') {
        const trainerBatches = await Batch.find({ 
            trainerId: req.user._id, 
            courseId: courseId, 
            isActive: true 
        });

        if (trainerBatches.length === 0) {
            throw new ApiError(403, 'Access denied. You are not assigned to any batches for this course.');
        }

        return next();
    }

    // Other roles are not allowed
    throw new ApiError(403, 'Access denied. Insufficient permissions.');
});

// Middleware to log batch access for audit purposes
const logBatchAccess = asyncHandler(async (req, res, next) => {
    const { batchId } = req.params || req.body;
    const userId = req.user._id;
    const userRole = req.user.role;
    const action = req.method;
    const endpoint = req.originalUrl;

    // Log the access attempt (you can extend this to save to database)
    console.log(`[BATCH ACCESS] User: ${userId} (${userRole}) | Action: ${action} | Endpoint: ${endpoint} | BatchId: ${batchId} | Time: ${new Date().toISOString()}`);
    
    next();
});

module.exports = {
    verifyBatchOwnership,
    verifyBatchOwnershipFromBody,
    verifyMultipleBatchOwnership,
    requireTrainerRole,
    requireBatchManagementRole,
    verifyCourseAccess,
    logBatchAccess
};
